﻿namespace ShinKenShinKun.UI;

public class Dimens
{
    public static readonly double Spacing0 = 0;
    public static readonly double Spacing1 = 1;
    public static readonly double Spacing2 = 2;
    public static readonly double Spacing3 = 3;
    public static readonly double Spacing5 = 5;
    public static readonly double Spacing7 = 7;
    public static readonly double Spacing10 = 10;
    public static readonly double Spacing12 = 12;
    public static readonly double Spacing14 = 14;
    public static readonly double Spacing15 = 15;
    public static readonly double Spacing16 = 16;
    public static readonly double Spacing18 = 18;
    public static readonly double Spacing20 = 20;
    public static readonly double Spacing24 = 24;
    public static readonly double Spacing28 = 28;
    public static readonly double Spacing30 = 30;
    public static readonly double Spacing36 = 36;
    public static readonly double Spacing40 = 40;
    public static readonly double Spacing50 = 50;
    public static readonly double Spacing60 = 60;
    public static readonly double Spacing70 = 70;
    public static readonly double Spacing74 = 74;
    public static readonly double Spacing80 = 80;
    public static readonly double Spacing100 = 100;
    public static readonly double Spacing120 = 120;
    public static readonly double Spacing122 = 122;
    public static readonly double Spacing145 = 145;
    public static readonly double Spacing160 = 160;
    public static readonly double Spacing200 = 200;
    public static readonly double Spacing340 = 340;
    public static readonly double Spacing500 = 500;
    public static readonly double SpacingLg = 24;
    public static readonly double SpacingMd = 16;
    public static readonly double SpacingMinus10 = -10;
    public static readonly double SpacingMinus3 = -3;
    public static readonly double SpacingMinus5 = -5;
    public static readonly double SpacingMinus8 = -8;
    public static readonly double SpacingS = 12;
    public static readonly double SpacingSm = 8;
    public static readonly double SpacingSm2 = 10;
    public static readonly double SpacingXl = 32;
    public static readonly double SpacingXs = 6;
    public static readonly double SpacingXxs = 4;
    public static readonly double SpacingXxs2 = 5;
    public static readonly double SpacingXxl = 48;

    public static readonly double Thickness0 = 0;
    public static readonly double Thickness1 = 1;
    public static readonly double Thickness2 = 2;
    public static readonly double Thickness3 = 3;
    public static readonly double Thickness4 = 4;

    public static readonly Thickness RadCornerRadius0 = new(0);
    public static readonly Thickness RadCornerRadius3 = new(3);
    public static readonly Thickness RadCornerRadius4 = new(4);
    public static readonly Thickness RadCornerRadius5 = new(5);
    public static readonly Thickness RadCornerRadius7 = new(7);
    public static readonly Thickness RadCornerRadius8 = new(8);
    public static readonly Thickness RadCornerRadius10 = new(10);
    public static readonly Thickness RadCornerRadius12 = new(12);
    public static readonly Thickness RadCornerRadius16 = new(16);

    public static readonly double FontSizeT8 = 10;
    public static readonly double FontSizeT7 = 12;
    public static readonly double FontSize13 = 13;
    public static readonly double FontSizeT6 = 14;
    public static readonly double FontSize15 = 15;
    public static readonly double FontSizeT5 = 16;
    public static readonly double FontSizeT4 = 20;
    public static readonly double FontSizeT3 = 24;
    public static readonly double FontSizeT2 = 32;
    public static readonly double FontSizeT1 = 36;
    public static readonly double FontSizeT9 = 16;
    public static readonly double FontSize28 = 28;
    public static readonly double FontSize35 = 35;
    public static readonly double FontSize50 = 50;
    public static readonly double FontSize60 = 60;
    public static readonly double FontSizeLoading = 28;
    public static readonly double MedicalFontSize = 12;
    public static readonly double PatientFontsize = 14;

    public static readonly double MinimumSlider = 36;
    public static readonly int MaximumAdditionalCharacter = 20;
    public static readonly int MaximumClientIdCharacter = 10;
    public static readonly double MaximumDivisionDispNameCharactor = 2;
    public static readonly double MaximumColHeaderWidthSlider = MedicalFontSize * MaximumAdditionalCharacter;
    public static readonly double MaximumColHeaderHeight = MedicalFontSize * MaximumClientIdCharacter;
    public static readonly double DefaultErrorWidth = 480;

    public static readonly double LineHeightT1 = 48;
    public static readonly double LineHeightT2 = 32;
    public static readonly double LineHeightT3 = 28;
    public static readonly double LineHeightT4 = 24;
    public static readonly double LineHeightT5 = 22;
    public static readonly double LineHeightT6 = 20;
    public static readonly double LineHeightT7 = 18;
    public static readonly double LineHeightT8 = 16;

    public static readonly double TextLineHeightT1 = 4.8;
    public static readonly double TextLineHeightT2 = 3.2;
    public static readonly double TextLineHeightT3 = 2.8;
    public static readonly double TextLineHeightT4 = 2.4;
    public static readonly double TextLineHeightT5 = 2.2;
    public static readonly double TextLineHeightT6 = 2.0;
    public static readonly double TextLineHeightT7 = 1.8;
    public static readonly double TextLineHeightT8 = 1.6;
    public static readonly double TextLineHeightT9 = 1.3;

    public static readonly double BoxSize17 = 17;
    public static readonly double BoxSize20 = 20;

    public static readonly int ButtonCornerRadius = 16;
    public static readonly int ButtonCornerRadius2 = 2;
    public static readonly int ButtonCornerRadius4 = 4;
    public static readonly int ButtonCornerRadius16 = 16;
    public static readonly int ButtonCornerRadiusXs = 12;
    public static readonly int ButtonCornerRadiusXXs = 8;

    public static readonly double ButtonHeight = 48;
    public static readonly double ButtonHeightSm = 46;
    public static readonly double ButtonHeightXs = 36;

    public static readonly double ButtonWidth = 252;
    public static readonly double ButtonWidth20 = 20;
    public static readonly double ButtonWidth200 = 200;

    public static readonly int TableBorder1 = 1;

    public static readonly int CornerRadius3 = 3;
    public static readonly int CornerRadius4 = 4;
    public static readonly int CornerRadius2 = 2;
    public static readonly int CornerRadius8 = 8;
    public static readonly int CornerRadius12 = 12;
    public static readonly int CornerRadius16 = 16;
    public static readonly int CornerRadius24 = 24;

    #region First Column

    public static readonly double DefaultAdditionalInformationHeight = 44;
    public static readonly double DefaultAgeHeight = 44;
    public static readonly double DefaultBoxViewWidth = 228;
    public static readonly double DefaultClientIdHeight = MedicalFontSize * MaximumClientIdCharacter;
    public static readonly double DefaultClientInfomationsWitdhChange = 46;
    public static readonly double DefaultColHeaderHeight = 100;
    public static readonly double DefaultColHeaderWidth = 36;
    public static readonly double DefaultCourseHeight = 128;
    public static readonly double DefaultElapsedHeight = 56;
    public static readonly double DefaultFinishedCountHeight = 36;
    public static readonly double DefaultFloorHeight = 44;
    public static readonly double DefaultFontSize = 14;
    public static readonly double DefaultGuideStatusHeight = 58;
    public static readonly double DefaultHeaderColumnHeight = 160;
    public static readonly double DefaultHeaderColumnWidth = 36;
    public static readonly double DefaultKanjiNameHeight = 100;
    public static readonly double DefaultKannaNameHeight = 100;
    public static readonly double DefaultMaximumFontSize = 28;
    public static readonly double DefaultMedicalCheckNameHeight = 120;
    public static readonly double DefaultMedicalCheckNameHeightChange = 0;
    public static readonly double DefaultRegistrationNoHeight = 100;
    public static readonly double DefaultRowHeaderHeight = 100;
    public static readonly double DefaultRowHeaderWidth = 36;
    public static readonly double DefaultSequentialNumberHeight = 48;
    public static readonly double DefaultSexHeight = 30;
    public static readonly double DefaultTargetCountHeight = 36;
    public static readonly double DefaultWaitHeight = 56;
    public static readonly double DefaultWaitingHeight = 36;

    #endregion First Column

    public static readonly double EntryCornerRadius = 12;
    public static readonly float EntryCornerRadius0 = 0;
    public static readonly double EntryHeight = 56;
    public static readonly double FontSize40 = 40;
    public static readonly double FontSize100 = 100;
    public static readonly double Height10 = 10;
    public static readonly double Height12 = 12;
    public static readonly double Height16 = 16;
    public static readonly double Height18 = 18;
    public static readonly double Height20 = 20;
    public static readonly double Height24 = 24;
    public static readonly double Height28 = 28;
    public static readonly double Height30 = 30;
    public static readonly double Height32 = 32;
    public static readonly double Height36 = 36;
    public static readonly double Height38 = 38;
    public static readonly double Height40 = 40;
    public static readonly double Height42 = 42;
    public static readonly double Height44 = 44;
    public static readonly double Height46 = 46;
    public static readonly double Height48 = 48;
    public static readonly double Height50 = 50;
    public static readonly double Height51 = 51;
    public static readonly double Height52 = 52;
    public static readonly double Height54 = 54;
    public static readonly double Height56 = 56;
    public static readonly double Height58 = 58;
    public static readonly double Height64 = 64;
    public static readonly double Height68 = 68;
    public static readonly double Height70 = 70;
    public static readonly double Height80 = 80;
    public static readonly double Height85 = 85;
    public static readonly double Height88 = 16;
    public static readonly double Height90 = 90;
    public static readonly double Height100 = 100;
    public static readonly double Height106 = 106;
    public static readonly double Height140 = 140;
    public static readonly double Height150 = 150;
    public static readonly double Height168 = 168;
    public static readonly double Height200 = 200;
    public static readonly double Height268 = 268;
    public static readonly double NumberPadHeight = 400;

    public static readonly double IconSize17 = 17;
    public static readonly double IconSize26 = 26;
    public static readonly double IconSize30 = 30;

    public static readonly double ImageRatioOfHeightOnWidth = 3.0 / 4;
    public static readonly double Opacity = 0.5;
    public static readonly double Opacity0 = 0;
    public static readonly double Opacity1 = 1;
    public static readonly float OpacityLight = (float)0.1;
    public static readonly float OpacityMedium = (float)0.5;

    public static readonly Thickness RadBorderCornerRadius0 = new(0);
    public static readonly Thickness RadBorderCornerRadius2 = new(2);
    public static readonly Thickness RadBorderCornerRadius4 = new(4);
    public static readonly Thickness RadBorderCornerRadius5 = new(5);
    public static readonly Thickness RadBorderCornerRadius8 = new(8);
    public static readonly Thickness RadBorderCornerRadius10 = new(10);
    public static readonly Thickness RadBorderCornerRadius12 = new(12);
    public static readonly Thickness RadBorderCornerRadius16 = new(16);
    public static readonly Thickness RadBorderCornerRadius24 = new(24);
    public static readonly Thickness RadBorderCornerRadius28 = new(28);

    public static readonly Thickness RadBorderThickness0 = new(0);
    public static readonly Thickness RadBorderThickness1 = new(1);
    public static readonly Thickness RadBorderThickness2 = new(2);
    public static readonly Thickness RadBorderThickness3 = new(3);
    public static readonly Thickness RadBorderThickness4 = new(4);

    public static readonly double RotationY0 = 0;
    public static readonly double RotationY180 = 180;

    public static readonly float RadiusShadowPopup10 = 10;
    public static readonly double ShadowRadius15 = 15;
    public static readonly float ShadowCornerRadius10 = 10;
    public static readonly double Thickness0_25 = 0.25;
    public static readonly double Thickness0_5 = 0.5;

    public static readonly double Width10 = 10;
    public static readonly double Width12 = 12;
    public static readonly double Width16 = 16;
    public static readonly double Width18 = 18;
    public static readonly double Width20 = 20;
    public static readonly double Width24 = 24;
    public static readonly double Width28 = 28;
    public static readonly double Width32 = 32;
    public static readonly double Width36 = 36;
    public static readonly double Width40 = 40;
    public static readonly double Width42 = 42;
    public static readonly double Width48 = 48;
    public static readonly double Width50 = 50;
    public static readonly double Width51 = 51;
    public static readonly double Width56 = 56;
    public static readonly double Width60 = 60;
    public static readonly double Width74 = 74;
    public static readonly double Width80 = 80;
    public static readonly double Width90 = 90;
    public static readonly double Width94 = 94;
    public static readonly double Width100 = 100;
    public static readonly double Width114 = 114;
    public static readonly double Width120 = 120;
    public static readonly double Width146 = 146;
    public static readonly double Width150 = 150;
    public static readonly double Width160 = 160;
    public static readonly double Width190 = 190;
    public static readonly double Width200 = 200;
    public static readonly double Width216 = 216;
    public static readonly double Width220 = 220;
    public static readonly double Width252 = 252;
    public static readonly double Width320 = 320;
    public static readonly double Width350 = 350;
    public static readonly double Width380 = 380;
    public static readonly double Width420 = 420;
    public static readonly double Width540 = 540;
}