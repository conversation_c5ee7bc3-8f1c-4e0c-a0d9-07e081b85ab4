<?xml version="1.0" encoding="utf-8" ?>
<mvvm:BasePage
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.GuidanceAdjustmentPage"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    x:DataType="app:GuidanceAdjustmentPageViewModel"
    xmlns:app="clr-namespace:ShinKenShinKun">
    <Shell.BackButtonBehavior>
        <BackButtonBehavior
            IsVisible="False"
            IsEnabled="False" />
    </Shell.BackButtonBehavior>
    <Grid
        BackgroundColor="{x:Static ui:AppColors.LightCobaltBlue}"
        RowDefinitions="80, *, Auto">
        <!--Header-->
        <Grid
            Grid.Row="0"
            ColumnDefinitions="*, Auto, *">
            <HorizontalStackLayout
                Grid.Column="0"
                VerticalOptions="Center"
                Spacing="{x:Static ui:Dimens.SpacingXl}"
                Margin="{ui:EdgeInsets Left={x:Static ui:Dimens.SpacingSm2}}">
                <app:HomeButtonView
                    HorizontalOptions="Start"
                    GoHomeCommand="{Binding GoHomeCommand}" />
                <app:MaskButtonView
                    HorizontalOptions="Start"
                    VerticalOptions="Center"
                    ChangeModeCommand="{Binding ChangeModeCommand}"
                    IsMaskMode="{Binding IsMaskMode}" />

            </HorizontalStackLayout>

            <HorizontalStackLayout
                Grid.Column="1"
                HorizontalOptions="Center"
                Spacing="{x:Static ui:Dimens.SpacingLg}">
                <Label
                    Text="{x:Static resources:AppResources.GuidanceAdjustment}"
                    Style="{Static ui:Styles.AppHeaderLabelStyle}" />
            </HorizontalStackLayout>

            <app:LoginUserView
                IsVisible="{Binding IsLogin}"
                LoginId="{Binding LoginId}"
                Grid.Column="2" />
        </Grid>

        <!--Content-->
        <Grid
            Grid.Row="1"
            ColumnDefinitions="3*,7*"
            BackgroundColor="{x:Static ui:AppColors.ContentBackgroud}">
            <app:PatientInfoView
                NoteScroll="{Binding NoteScroll}"
                PersonalNoteScroll="{Binding PersonalNoteScroll}"
                ShowNoteEditPopupCommand="{Binding ShowNoteEditPopupCommand}"
                IsMaskMode="{Binding IsMaskMode}"
                IsItemSelected="{Binding ClientSelected,
                    Converter={x:Static ui:AppConverters.ObjectToBoolConverter}}"
                ItemSelected="{Binding ClientSelected}"
                Grid.Column="0" />
            <Grid
                Grid.Column="1"
                Padding="{x:Static ui:Dimens.SpacingSm2}"
                RowSpacing="{x:Static ui:Dimens.SpacingSm2}"
                RowDefinitions="*, auto">
                <telerik:RadBorder
                    Grid.Row="0"
                    Padding="{ui:EdgeInsets All={x:Static ui:Dimens.SpacingMd}}"
                    Style="{x:Static ui:Styles.FrameBorderStyle}">
                    <Grid
                        RowSpacing="{x:Static ui:Dimens.SpacingMd}"
                        RowDefinitions="auto, *">
                        <app:PatientGuidanceFilterView
                            MedicalCheckDivisions="{Binding MedicalCheckDivisions}"
                            MedicalCheckSelected="{Binding MedicalCheckSelected}"
                            ClientIdSearched="{Binding ClientIdSearched}"
                            SearchCommand="{Binding SearchByClientIdCommand}"
                            Grid.Row="0" />
                        <app:PatientGuidanceTableView
                            ItemsSource="{Binding ClientInformations}"
                            ResetScrollCommand="{Binding ResetScrollCommand}"
                            ItemSelected="{Binding ClientSelected}"
                            IsMaskMode="{Binding IsMaskMode}"
                            Grid.Row="1" />
                    </Grid>
                </telerik:RadBorder>
                <telerik:RadBorder
                    Grid.Row="1"
                    Padding="{ui:EdgeInsets All={x:Static ui:Dimens.SpacingMd}}"
                    MinimumHeightRequest="{x:Static ui:Dimens.Height140}"
                    Style="{x:Static ui:Styles.FrameBorderStyle}">
                    <app:PatientListView
                        ItemsSource="{Binding ClientSelected.MedicalCheckStateDisplays}" />
                </telerik:RadBorder>
            </Grid>
        </Grid>

        <!--Footer-->
        <Grid
            BackgroundColor="{x:Static ui:AppColors.LightCobaltBlue}"
            Padding="{x:Static ui:Dimens.SpacingSm2}"
            HeightRequest="{x:Static ui:Dimens.Height100}"
            Grid.Row="2"
            ColumnDefinitions="Auto,*,Auto">
            <app:BackButtonView
                BackCommand="{Binding BackCommand}"
                Grid.Column="0" />
            <HorizontalStackLayout
                Grid.Column="2"
                Spacing="{x:Static ui:Dimens.SpacingMd}">
                <telerik:RadBorder
                    Style="{x:Static ui:Styles.PendingAllBorderStyle}">
                    <VerticalStackLayout
                        HorizontalOptions="Center"
                        VerticalOptions="Center">
                        <Image
                            Aspect="Fill"
                            Source="{x:Static ui:Icons.PendingAllIcon}"
                            HeightRequest="{x:Static ui:Dimens.SpacingXl}"
                            WidthRequest="{x:Static ui:Dimens.SpacingXl}" />
                        <Label
                            Style="{x:Static ui:Styles.PendingAllLabelStyle}" />
                    </VerticalStackLayout>
                    <telerik:RadBorder.GestureRecognizers>
                        <TapGestureRecognizer
                            Command="{Binding GoToPatientPendingCommand}" />
                    </telerik:RadBorder.GestureRecognizers>
                </telerik:RadBorder>
                <telerik:RadBorder
                    Style="{x:Static ui:Styles.PendingBorderStyle}">
                    <VerticalStackLayout
                        HorizontalOptions="Center"
                        VerticalOptions="Center">
                        <Image
                            Aspect="Fill"
                            Source="{x:Static ui:Icons.PauseCircle}"
                            HeightRequest="{x:Static ui:Dimens.SpacingXl}"
                            WidthRequest="{x:Static ui:Dimens.SpacingXl}" />
                        <Label
                            Style="{x:Static ui:Styles.LabelPendingStyle}" />
                    </VerticalStackLayout>
                    <telerik:RadBorder.GestureRecognizers>
                        <TapGestureRecognizer
                            Command="{Binding GoToPendingTestSelectionCommand}" />
                    </telerik:RadBorder.GestureRecognizers>
                </telerik:RadBorder>
                <telerik:RadBorder
                    Style="{x:Static ui:Styles.StopBorderStyle}">
                    <VerticalStackLayout
                        HorizontalOptions="Center"
                        VerticalOptions="Center">
                        <Image
                            Aspect="Fill"
                            Source="{x:Static ui:Icons.BanIcon}"
                            HeightRequest="{x:Static ui:Dimens.SpacingXl}"
                            WidthRequest="{x:Static ui:Dimens.SpacingXl}" />
                        <Label
                            Style="{x:Static ui:Styles.LabelStopStyle}" />
                    </VerticalStackLayout>
                    <telerik:RadBorder.GestureRecognizers>
                        <TapGestureRecognizer
                            Command="{Binding GoToStopExamineCommand}" />
                    </telerik:RadBorder.GestureRecognizers>
                </telerik:RadBorder>
                <telerik:RadBorder
                    Style="{x:Static ui:Styles.ChangeGuideBorderStyle}"
                    Margin="{ui:EdgeInsets Left={x:Static ui:Dimens.SpacingXl}}">
                    <VerticalStackLayout
                        HorizontalOptions="Center"
                        VerticalOptions="Center">
                        <Image
                            Source="{x:Static ui:Icons.ChangeGuideIcon}"
                            HeightRequest="{x:Static ui:Dimens.Spacing36}"
                            WidthRequest="{x:Static ui:Dimens.Spacing36}" />
                        <Label
                            Style="{x:Static ui:Styles.ChangeGuideLabelStyle}" />
                    </VerticalStackLayout>
                    <telerik:RadBorder.GestureRecognizers>
                        <TapGestureRecognizer
                            Command="{Binding GoToGuidanceSettingCommand}" />
                    </telerik:RadBorder.GestureRecognizers>
                    <telerik:RadBorder.Triggers>
                        <DataTrigger
                            TargetType="telerik:RadBorder"
                            Binding="{Binding ClientSelected,
                                Converter={x:Static ui:AppConverters.ObjectToBoolConverter}}"
                            Value="False">
                            <Setter
                                Property="Opacity"
                                Value="0.7" />
                        </DataTrigger>
                    </telerik:RadBorder.Triggers>
                </telerik:RadBorder>
            </HorizontalStackLayout>
        </Grid>

    </Grid>
</mvvm:BasePage>