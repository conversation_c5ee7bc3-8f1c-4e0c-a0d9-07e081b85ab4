﻿namespace ShinKenShinKun.UI;

public partial class Styles
{
    public static Style UserIdPasswordRadBorderStyle => CreateStyle<RadBorder>()
        .Set(Layout.PaddingProperty,
            new Thickness(
                left: Dimens.Spacing15,
                top: Dimens.Spacing5,
                right: Dimens.SpacingSm,
                bottom: Dimens.Spacing3
            ))
        .Set(RadBorder.CornerRadiusProperty, Dimens.RadBorderCornerRadius4)
        .Set(VisualElement.BackgroundColorProperty, AppColors.LightSteelBlue)
        .Set(VisualElement.HeightRequestProperty, Dimens.Height50);

    public static Style CustomRadBorderStyle => CreateStyle<RadBorder>()
        .Set(RadBorder.BorderThicknessProperty, Dimens.RadBorderThickness2)
        .Set(RadBorder.BorderBrushProperty, AppColors.Black)
        .Set(View.MarginProperty, Dimens.Spacing1)
        .Set(VisualElement.HeightRequestProperty, Dimens.Height70)
        .Set(RadBorder.CornerRadiusProperty, new Thickness(Dimens.Spacing15, 0, 0, Dimens.Spacing15));

    public static Style CustomBaseRadBorderStyle => CreateStyle<RadBorder>()
        .Set(VisualElement.ShadowProperty, new Shadow()
        {
            Brush = new SolidColorBrush(AppColors.Black),
            Offset = new Microsoft.Maui.Graphics.Point(0, 0),
            Radius = 3,
            Opacity = 0.3f
        })
        .Set(RadBorder.CornerRadiusProperty, Dimens.RadBorderCornerRadius10);
    public static Style CustomMarginRadBorderStyle => CreateStyle<RadBorder>()
        .BaseOn(CustomBaseRadBorderStyle)
        .Set(View.MarginProperty, new Thickness(Dimens.Spacing40, Dimens.Spacing20));

    public static Style CustomPaddingRadBorderStyle => CreateStyle<RadBorder>()
        .BaseOn(CustomBaseRadBorderStyle)
        .Set(RadBorder.MarginProperty, new Thickness(Dimens.Spacing100, Dimens.Spacing30, Dimens.Spacing100, Dimens.Spacing15))
        .Set(RadBorder.BackgroundColorProperty, AppColors.TightBlue)
        .Set(RadBorder.CornerRadiusProperty, new Thickness(Dimens.Spacing40, 0, 0, Dimens.Spacing40));

    public static Style BackBorderStyle => CreateStyle<RadBorder>()
        .Set(VisualElement.BackgroundColorProperty, AppColors.GrayBack)
        .Set(RadBorder.CornerRadiusProperty, Dimens.RadCornerRadius10)
        .Set(VisualElement.WidthRequestProperty, Dimens.Width160);

    public static Style InputClientIdBorderStyle => CreateStyle<RadBorder>()
        .Set(VisualElement.BackgroundColorProperty, AppColors.DarkGray)
        .Set(RadBorder.CornerRadiusProperty, Dimens.RadCornerRadius10)
        .Set(VisualElement.WidthRequestProperty, Dimens.Width80);

    public static Style PendingBorderStyle => CreateStyle<RadBorder>()
        .Set(VisualElement.BackgroundColorProperty, AppColors.MediumGray)
        .Set(RadBorder.CornerRadiusProperty, Dimens.RadCornerRadius10)
        .Set(VisualElement.WidthRequestProperty, Dimens.Width80);

    public static Style StopBorderStyle => CreateStyle<RadBorder>()
        .Set(VisualElement.BackgroundColorProperty, AppColors.Red)
        .Set(RadBorder.CornerRadiusProperty, Dimens.RadCornerRadius10)
        .Set(VisualElement.WidthRequestProperty, Dimens.Width80);

    public static Style HeaderEmptyContentBorderStyle => CreateStyle<RadBorder>()
        .Set(VisualElement.BackgroundColorProperty, AppColors.DarkLightBlueGray)
        .Set(RadBorder.BorderColorProperty, AppColors.LightSkyBlue)
        .Set(VisualElement.HeightRequestProperty, Dimens.Height50)
        .Set(RadBorder.BorderThicknessProperty, Dimens.RadBorderThickness1);

    public static Style MaskBorderStyle => CreateStyle<RadBorder>()
       .Set(RadBorder.BackgroundColorProperty, AppColors.White)
       .Set(RadBorder.HeightRequestProperty, Dimens.Height38)
       .Set(RadBorder.WidthRequestProperty, Dimens.Width146)
       .Set(RadBorder.CornerRadiusProperty, Dimens.RadCornerRadius5);

    public static Style BackButtonBorderStyle => CreateStyle<RadBorder>()
        .Set(RadBorder.BackgroundColorProperty, AppColors.GrayBack)
        .Set(RadBorder.CornerRadiusProperty, Dimens.RadCornerRadius10)
        .Set(RadBorder.PaddingProperty, Dimens.SpacingSm2)
        .Set(RadBorder.WidthRequestProperty, Dimens.Spacing160);

    public static Style StopButtonBorderStyle => CreateStyle<RadBorder>()
        .Set(RadBorder.BackgroundColorProperty, AppColors.Red)
        .Set(RadBorder.CornerRadiusProperty, Dimens.RadCornerRadius10)
        .Set(RadBorder.PaddingProperty, Dimens.SpacingSm2)
        .Set(RadBorder.WidthRequestProperty, Dimens.Spacing80)
        .Set(RadBorder.HorizontalOptionsProperty, LayoutOptions.Start);

    public static Style SaveButtonBorderStyle => CreateStyle<RadBorder>()
        .Set(RadBorder.HorizontalOptionsProperty, LayoutOptions.End)
        .Set(RadBorder.WidthRequestProperty, Dimens.Width200)
        .Set(RadBorder.BackgroundColorProperty, AppColors.DarkSlateGray)
        .Set(RadBorder.CornerRadiusProperty, Dimens.RadCornerRadius10)
        .Set(RadBorder.PaddingProperty, Dimens.SpacingSm2)
        .Set(RadBorder.WidthRequestProperty, Dimens.Spacing200);

    public static Style NoteEditDialogBorderStyle => CreateStyle<RadBorder>()
        .Set(VisualElement.BackgroundColorProperty, AppColors.White)
        .Set(RadBorder.BorderThicknessProperty, Dimens.RadBorderThickness1)
        .Set(RadBorder.BorderColorProperty, AppColors.BorderColorPopup)
        .Set(RadBorder.CornerRadiusProperty, Dimens.RadBorderCornerRadius12)
       ;

    public static Style PatientNotesBorderStyle => CreateStyle<RadBorder>()
        .Set(VisualElement.BackgroundColorProperty, AppColors.LightPeriwinkleBlue)
        .Set(RadBorder.CornerRadiusProperty, Dimens.RadCornerRadius8);

    public static Style CloseNoteBorderStyle => CreateStyle<RadBorder>()
        .Set(VisualElement.BackgroundColorProperty, AppColors.White)
        .Set(VisualElement.WidthRequestProperty, Dimens.Width24)
        .Set(VisualElement.HeightRequestProperty, Dimens.Width24)
        .Set(RadBorder.CornerRadiusProperty, Dimens.RadBorderCornerRadius24);

    public static Style ExamineFilterBorderStyle => CreateStyle<RadBorder>()
        .Set(VisualElement.BackgroundColorProperty, AppColors.LightNormalGray)
        .Set(RadBorder.CornerRadiusProperty, Dimens.RadCornerRadius8)
        .Set(RadBorder.BorderThicknessProperty, Dimens.RadBorderThickness0);
    public static Style IdInputDisplayBorderStyle => CreateStyle<RadBorder>()
        .Set(RadBorder.BorderThicknessProperty, Dimens.RadBorderThickness0)
        .Set(RadBorder.PaddingProperty, Dimens.Spacing10)
        .Set(RadBorder.HeightRequestProperty, Dimens.Height106)
        .Set(RadBorder.BackgroundColorProperty, AppColors.White);

    public static Style SearchBorderStyle => CreateStyle<RadBorder>()
        .Set(VisualElement.BackgroundColorProperty, AppColors.LightGrayishBlue)
        .Set(RadBorder.CornerRadiusProperty, Dimens.RadCornerRadius8)
        .Set(VisualElement.HeightRequestProperty, Dimens.Height42)
        .Set(RadBorder.BorderThicknessProperty, Dimens.RadBorderThickness0);

    public static Style PendingAllBorderStyle => CreateStyle<RadBorder>()
        .Set(VisualElement.BackgroundColorProperty, AppColors.MediumGray)
        .Set(RadBorder.CornerRadiusProperty, Dimens.RadCornerRadius8)
        .Set(VisualElement.WidthRequestProperty, Dimens.Width80);

    public static Style ChangeGuideBorderStyle => CreateStyle<RadBorder>()
        .Set(VisualElement.BackgroundColorProperty, AppColors.DarkIndigo)
        .Set(RadBorder.CornerRadiusProperty, Dimens.RadCornerRadius8)
        .Set(VisualElement.WidthRequestProperty, Dimens.Width220);

    public static Style PendingTestAllBorderStyle => CreateStyle<RadBorder>()
        .Set(VisualElement.BackgroundColorProperty, AppColors.MediumLightGray)
        .Set(RadBorder.CornerRadiusProperty, Dimens.RadCornerRadius10)
        .Set(VisualElement.WidthRequestProperty, Dimens.Width80);
    public static Style CancelAllBorderStyle => CreateStyle<RadBorder>()
        .Set(VisualElement.BackgroundColorProperty, AppColors.DarkGray)
        .Set(RadBorder.CornerRadiusProperty, Dimens.RadCornerRadius10)
        .Set(VisualElement.WidthRequestProperty, Dimens.Width80);

    public static Style StopTestAllBorderStyle => CreateStyle<RadBorder>()
        .Set(VisualElement.BackgroundColorProperty, AppColors.Red)
        .Set(RadBorder.CornerRadiusProperty, Dimens.RadCornerRadius10)
        .Set(VisualElement.WidthRequestProperty, Dimens.Width80);

    public static Style MaskOnOffSwitchModeBorder => CreateStyle<RadBorder>()
        .Set(RadBorder.BorderColorProperty, AppColors.Transparent)
        .Set(RadBorder.CornerRadiusProperty, Dimens.RadCornerRadius4)
        .Set(VisualElement.BackgroundColorProperty, AppColors.DarkBlue)
        .Set(VisualElement.HeightRequestProperty, Dimens.Height30)
        .Set(VisualElement.WidthRequestProperty, Dimens.Width60);

    public static Style ExamineEditBorder => CreateStyle<RadBorder>()
        .Set(VisualElement.WidthRequestProperty, Dimens.Width120)
        .Set(VisualElement.HeightRequestProperty, Dimens.Height40)
        .Set(RadBorder.CornerRadiusProperty, Dimens.CornerRadius8);

    public static Style BlackThinBorderStyle => CreateStyle<RadBorder>()
        .Set(RadBorder.BorderThicknessProperty, Dimens.Thickness1)
        .Set(RadBorder.BorderColorProperty, AppColors.Black);

    public static Style BorderStateButtonStyle => CreateStyle<RadBorder>()
        .Set(RadBorder.BorderColorProperty, AppColors.SoftGray)
        .Set(RadBorder.BorderThicknessProperty, Dimens.Thickness2)
        .Set(RadBorder.CornerRadiusProperty, Dimens.RadCornerRadius8);
}