﻿namespace ShinKenShinKun;

public partial class AppShell : Shell
{
    public AppShell()
    {
        InitializeComponent();
        Routing.RegisterRoute(RouterName.LoginPage, typeof(LoginPage));
        Routing.RegisterRoute(RouterName.HomePage, typeof(HomePage));
        Routing.RegisterRoute(RouterName.HomePageRoot, typeof(HomePage));
        Routing.RegisterRoute(RouterName.TestSelectionPage, typeof(TestSelectionPage));
        Routing.RegisterRoute(RouterName.ExamineBeforeAuthPage, typeof(ExamineBeforeAuthPage));
        Routing.RegisterRoute(RouterName.GuidanceSelectionPage, typeof(GuidanceSelectionPage));
        Routing.RegisterRoute(RouterName.GuidanceSettingPage, typeof(GuidanceSettingPage));
        Routing.RegisterRoute(RouterName.ExamineAfterAuthPage, typeof(ExamineAfterAuthPage));
        Routing.RegisterRoute(RouterName.IndividualProgressPage, typeof(IndividualProgressPage));
        Routing.RegisterRoute(RouterName.PatientPendingPage, typeof(PatientPendingPage));
        Routing.RegisterRoute(RouterName.PatientGuidanceAdjustmentPage, typeof(GuidanceAdjustmentPage));
        Routing.RegisterRoute(RouterName.PendingTestSelectionPage, typeof(PendingTestSelectionPage));
        Routing.RegisterRoute(RouterName.StopExaminePage, typeof(StopExaminePage));
        Routing.RegisterRoute(RouterName.StopTestSelectionPage, typeof(StopTestSelectionPage));
        Routing.RegisterRoute(RouterName.BlankPage, typeof(BlankPage));
        Routing.RegisterRoute(RouterName.SelectCheckPage, typeof(SelectCheckPage));
    }
}