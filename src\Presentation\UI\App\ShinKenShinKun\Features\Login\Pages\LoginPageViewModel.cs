﻿namespace ShinKenShinKun;

public partial class LoginPageViewModel(
    IAppNavigator appNavigator,
    IAuthServices authServices,
    INetworkService networkService,
    IAppSettingServices appSettingServices,
    ISessionServices sessionServices,
    ILogService logService,
    IPopupService popupService
) : NavigationAwareBaseViewModel(appNavigator)
{
    #region ObservableProperty

    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(IsOfflineMode))]
    private bool isOnlineMode = true;

    #endregion ObservableProperty

    #region Property

    public bool IsOfflineMode => !IsOnlineMode;

    public LoginModel LoginForm { get; set; } = new();

    #endregion Property

    #region OverrideMethod

    public override async Task OnDisappearingAsync()
    {
        await base.OnDisappearingAsync();
    }

    #endregion OverrideMethod

    #region RelayCommand

    [RelayCommand]
    private void ChangeMode()
    {
        IsOnlineMode = !IsOnlineMode;
    }

    [RelayCommand]
    private async Task Login()
    {
        if(!LoginForm.IsValid())
        {
            if(string.IsNullOrWhiteSpace(LoginForm.LoginId))
            {
                await AppNavigator.ShowNotificationDialog(AppResources.LoginIdEmptyInvalid);
                return;
            }
            if(string.IsNullOrWhiteSpace(LoginForm.Password))
            {
                await AppNavigator.ShowNotificationDialog(AppResources.PasswordEmptyInvalid);
                return;
            }
        }

        var ipAddress = networkService.GetIPv4Addresses().FirstOrDefault() ?? string.Empty;

        popupService.ShowPopup<LoadingIndicatorViewModel>();
        var result = await authServices.LoginAsync(LoginForm.LoginId, LoginForm.Password) ?? null;
        popupService.ClosePopup();

        if(result == null)
        {
            logService.LogInformation(string.Format(AppResources.LogFormat, "LOGIN", LoginForm.LoginId, ipAddress, "Failed"));
            await AppNavigator.ShowNotificationDialog(AppResources.ConnectionErrors);
            return;
        }

        if(result.Status != (byte)LoginStatus.Successed)
        {
            logService.LogInformation(string.Format(AppResources.LogFormat, "LOGIN", LoginForm.LoginId, ipAddress, "Failed"));
            await AppNavigator.ShowNotificationDialog(AppResources.LoginIdOrPasswordInvalid);
            return;
        }

        logService.LogInformation(string.Format(AppResources.LogFormat, "LOGIN", LoginForm.LoginId, ipAddress, "Success"));
        sessionServices.LoginId = result.UserName;
        LoginForm.Clear();

        await AppNavigator.NavigateAsync(RouteHelpers.GetRouteAfterLogin(appSettingServices.AppSettings));
        if(appSettingServices.AppSettings.DefaultScreen == AppConstants.OverallProgressScreenCode)
        {
            await RouteHelpers.RunMonitoringSystem(appSettingServices.AppSettings);
        }
    }

    [RelayCommand]
    private async Task OpenQuitAppPopup()
    {
        if(await AppNavigator.ShowConfirmationDialog(AppResources.QuitAppTitle))
        {
            if(await AppNavigator.ShowConfirmationDialog(AppResources.ShutdownDeviceTitle))
                ShutdownDevice();
            else
                QuitApp();
        }
    }

    #endregion RelayCommand

    #region Private Method

    private static void QuitApp()
    {
        Application.Current?.Quit();
    }

    private static void ShutdownDevice()
    {
        QuitApp();
#if WINDOWS
        Process.Start("shutdown", "/s /t 0");
#endif
    }

    #endregion Private Method
}