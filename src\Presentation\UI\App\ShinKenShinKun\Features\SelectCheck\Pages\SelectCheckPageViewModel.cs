namespace ShinKenShinKun;

public partial class SelectCheckPageViewModel(IAppNavigator appNavigator, IAppSettingServices appSettingServices) : NavigationAwareBaseViewModel(appNavigator)
{
    internal Action hiddenEntryFocus;

    [ObservableProperty]
    private ObservableCollection<SelectCheckItem> selectCheckItems = [];

    public override Task OnAppearingAsync()
    {
        LoadSelectCheckItems();
        return Task.CompletedTask;
    }

    private void LoadSelectCheckItems()
    {
        var settings = appSettingServices.GetSelectCheckSettings();

        // If settings has select items, use them; otherwise use default from AppConstants
        if (settings.SelectItems.Any())
        {
            SelectCheckItems = settings.SelectItems.ToObservableCollection();
        }
        else
        {
            var items = new List<SelectCheckItem>();

            // Create medical check items based on AppConstants.MedicalCheckNames
            for (int i = 0; i < AppConstants.MedicalCheckNames.Count; i++)
            {
                items.Add(new SelectCheckItem
                {
                    TermId = (i + 1).ToString(),
                    Name = AppConstants.MedicalCheckNames[i]
                });
            }

            SelectCheckItems = items.ToObservableCollection();
        }
    }

    [RelayCommand]
    private async Task SelectCheckItem(SelectCheckItem item)
    {
        if (item != null)
        {
            item.IsSelected = true;
            await Task.Delay(100);
            hiddenEntryFocus?.Invoke();

            // TODO: Add navigation logic here based on selected item
            // For now, just reset selection
            item.IsSelected = false;
        }
    }

    [RelayCommand]
    private async Task GoHome()
    {
        await AppNavigator.NavigateAsync(RouterName.HomePageRoot);
    }
}
