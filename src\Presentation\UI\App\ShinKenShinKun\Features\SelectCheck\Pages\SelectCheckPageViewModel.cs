namespace ShinKenShinKun;

public partial class SelectCheckPageViewModel(IAppNavigator appNavigator, IAppSettingServices appSettingServices) : NavigationAwareBaseViewModel(appNavigator)
{
    internal Action hiddenEntryFocus;

    [ObservableProperty]
    private ObservableCollection<SelectCheckItem> selectCheckItems = [];

    public override Task OnAppearingAsync()
    {
        LoadSelectCheckItems();
        return Task.CompletedTask;
    }

    private void LoadSelectCheckItems()
    {
        var settings = appSettingServices.GetSelectCheckSettings();

        // If settings has select items, use them; otherwise use default from AppConstants
        if (settings.SelectItems.Any())
        {
            var items = settings.SelectItems.Select(item => new SelectCheckItem
            {
                TermId = item.TermId,
                Name = item.Name
            }).ToList();
            SelectCheckItems = items.ToObservableCollection();
        }
        else
        {
            var items = new List<SelectCheckItem>();

            // Create medical check items based on AppConstants.MedicalCheckNames
            for (int i = 0; i < AppConstants.MedicalCheckNames.Count; i++)
            {
                items.Add(new SelectCheckItem
                {
                    TermId = (i + 1).ToString(),
                    Name = AppConstants.MedicalCheckNames[i]
                });
            }

            SelectCheckItems = items.ToObservableCollection();
        }
    }

    [RelayCommand]
    private async Task SelectCheckItem(SelectCheckItem item)
    {
        if (item != null)
        {
            item.IsSelected = true;
            await Task.Delay(100);
            hiddenEntryFocus?.Invoke();

            try
            {
                // Navigate to detail page with the selected check
                var navigationParameter = new Dictionary<string, object>
                {
                    ["checkId"] = item.TermId,
                    ["checkName"] = item.Name
                };

                await Shell.Current.GoToAsync("checkdetail", navigationParameter);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Navigation error: {ex.Message}");
            }
            finally
            {
                // Reset selection
                item.IsSelected = false;
            }
        }
    }

    [RelayCommand]
    private async Task GoHome()
    {
        await AppNavigator.NavigateAsync(RouterName.HomePageRoot);
    }
}
