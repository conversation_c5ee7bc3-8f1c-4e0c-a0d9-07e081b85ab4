<mvvm:BasePage
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.ExamineAfterAuthPage"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:mtk="clr-namespace:MemoryToolkit.Maui;assembly=MemoryToolkit.Maui"
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns:converter="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    mtk:LeakMonitorBehavior.Cascade="True"
    mtk:TearDownBehavior.Cascade="False"
    x:DataType="app:ExamineAfterAuthViewModel"
    xmlns:app="clr-namespace:ShinKenShinKun">
    <Shell.BackButtonBehavior>
        <BackButtonBehavior
            IsVisible="False"
            IsEnabled="False" />
    </Shell.BackButtonBehavior>
    <Grid
        BackgroundColor="{x:Static ui:AppColors.HeaderColor}"
        RowDefinitions="80, *, Auto">
        <Grid
            Grid.Row="0"
            ColumnDefinitions="*, Auto, *">
            <app:MaskButtonView
                Grid.Column="0"
                HorizontalOptions="Start"
                VerticalOptions="Center"
                Margin="{ui:EdgeInsets Left={x:Static ui:Dimens.Spacing122}}"
                ChangeModeCommand="{Binding ChangeModeCommand}"
                IsMaskMode="{Binding IsMaskMode}" />

            <HorizontalStackLayout
                Grid.Column="1"
                HorizontalOptions="Center"
                Spacing="{x:Static ui:Dimens.SpacingLg}">
                <Label
                    Text="{x:Static resources:AppResources.ExaminationScreen}"
                    Style="{Static ui:Styles.AppHeaderLabelStyle}" />
                <Label
                    Text="{Binding MedicalMachineName}"
                    Style="{Static ui:Styles.AppHeaderLabelStyle}" />
            </HorizontalStackLayout>

            <app:LoginUserView
                IsVisible="{Binding IsLogin}"
                LoginId="{Binding LoginId}"
                Grid.Column="2" />
        </Grid>

        <Grid
            Grid.Row="1"
            ColumnDefinitions="3*,7*"
            BackgroundColor="{x:Static ui:AppColors.ContentBackgroud}">
            <app:PatientInfoView
                NoteScroll="{Binding NoteScroll}"
                PersonalNoteScroll="{Binding PersonalNoteScroll}"
                ShowNoteEditPopupCommand="{Binding ShowNoteEditPopupCommand}"
                IsMaskMode="{Binding IsMaskMode}"
                IsItemSelected="{Binding ClientSelected,
                    Converter={x:Static ui:AppConverters.ObjectToBoolConverter}}"
                ItemSelected="{Binding ClientSelected}"
                Grid.Column="0" />
            <Grid
                Grid.Column="1"
                Padding="{x:Static ui:Dimens.SpacingSm2}"
                RowSpacing="{x:Static ui:Dimens.SpacingSm2}"
                RowDefinitions="*,auto">
                <telerik:RadBorder
                    Grid.Row="0"
                    Padding="{ui:EdgeInsets All={x:Static ui:Dimens.SpacingMd}}"
                    Style="{x:Static ui:Styles.FrameBorderStyle}">
                    <ScrollView
                        x:Name="AreaLayout"
                        Orientation="Vertical" />
                </telerik:RadBorder>

                <telerik:RadBorder
                    Grid.Row="1"
                    Padding="{ui:EdgeInsets All={x:Static ui:Dimens.SpacingMd}}"
                    Style="{x:Static ui:Styles.FrameBorderStyle}">
                    <app:PatientListView
                        ItemsSource="{Binding ClientSelected.MedicalCheckStateDisplays}" />
                </telerik:RadBorder>
            </Grid>

        </Grid>

        <Grid
            BackgroundColor="{x:Static ui:AppColors.LightBlue}"
            Padding="{x:Static ui:Dimens.SpacingSm2}"
            HeightRequest="{x:Static ui:Dimens.Spacing100}"
            Grid.Row="2"
            ColumnDefinitions="Auto,*,Auto">
            <HorizontalStackLayout
                Grid.Column="0"
                HorizontalOptions="Start"
                Spacing="{x:Static ui:Dimens.Spacing24}">
                <app:BackButtonView
                    BackCommand="{Binding BackCommand}" />
                <app:ExaminationFunctionButtonView
                    WidthRequest="{x:Static ui:Dimens.Width540}"
                    MenuButtons="{Binding FunctionButtons}" />
            </HorizontalStackLayout>
            <HorizontalStackLayout
                Grid.Column="2"
                Spacing="{x:Static ui:Dimens.SpacingMd}">
                <telerik:RadBorder
                    Style="{x:Static ui:Styles.PendingBorderStyle}">
                    <VerticalStackLayout
                        HorizontalOptions="Center"
                        VerticalOptions="Center">
                        <Image
                            Aspect="Fill"
                            Source="{x:Static ui:Icons.PauseCircle}"
                            HeightRequest="{x:Static ui:Dimens.SpacingXl}"
                            WidthRequest="{x:Static ui:Dimens.SpacingXl}" />
                        <Label
                            Style="{x:Static ui:Styles.LabelPendingStyle}" />
                    </VerticalStackLayout>
                    <telerik:RadBorder.GestureRecognizers>
                        <TapGestureRecognizer
                            Command="{Binding GoToPendingTestSelectionCommand}" />
                    </telerik:RadBorder.GestureRecognizers>
                </telerik:RadBorder>
                <telerik:RadBorder
                    Style="{x:Static ui:Styles.StopButtonBorderStyle}">
                    <VerticalStackLayout
                        HorizontalOptions="Center"
                        VerticalOptions="Center">
                        <Image
                            Source="{x:Static ui:Icons.BanIcon}"
                            HeightRequest="{x:Static ui:Dimens.Height32}"
                            WidthRequest="{x:Static ui:Dimens.Width32}" />
                        <Label
                            Style="{x:Static ui:Styles.LabelStopStyle}" />
                    </VerticalStackLayout>
                    <telerik:RadBorder.GestureRecognizers>
                        <TapGestureRecognizer
                            Command="{Binding GoToStopTestSelectionCommand}" />
                    </telerik:RadBorder.GestureRecognizers>
                </telerik:RadBorder>
                <telerik:RadBorder
                    Style="{x:Static ui:Styles.SaveButtonBorderStyle}">
                    <telerik:RadBorder.GestureRecognizers>
                        <TapGestureRecognizer
                            Command="{Binding GoToGuidanceSelectionCommand}" />
                    </telerik:RadBorder.GestureRecognizers>
                    <VerticalStackLayout
                        HorizontalOptions="Center"
                        VerticalOptions="Center">
                        <Image
                            Source="{x:Static ui:Icons.SaveIcon}"
                            HeightRequest="{x:Static ui:Dimens.SpacingXl}"
                            WidthRequest="{x:Static ui:Dimens.SpacingXl}" />
                        <Label
                            Style="{x:Static ui:Styles.SaveButtonLabelStyle}" />
                    </VerticalStackLayout>
                </telerik:RadBorder>
            </HorizontalStackLayout>

        </Grid>
    </Grid>
</mvvm:BasePage>