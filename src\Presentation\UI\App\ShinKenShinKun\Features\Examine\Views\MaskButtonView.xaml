<?xml version="1.0" encoding="utf-8" ?>
<telerik:RadBorder
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.MaskButtonView"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:app="clr-namespace:ShinKenShinKun"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    CornerRadius="{x:Static ui:Dimens.RadCornerRadius10}"
    x:Name="this">
    <Grid
        BackgroundColor="{x:Static ui:AppColors.LightBlue2}"
        HeightRequest="{x:Static ui:Dimens.Height46}"
        WidthRequest="{x:Static ui:Dimens.Width216}"
        Padding="{ui:EdgeInsets Right={x:Static ui:Dimens.SpacingSm2}}"
        ColumnDefinitions="Auto,*">
        <Label
            Style="{x:Static ui:Styles.LabelMaskedStyle}"
            Grid.Column="0" />
        <telerik:RadBorder
            BackgroundColor="{x:Static ui:AppColors.White}"
            HeightRequest="{x:Static ui:Dimens.Height38}"
            WidthRequest="{x:Static ui:Dimens.Width146}"
            CornerRadius="{x:Static ui:Dimens.RadBorderCornerRadius5}"
            Grid.Column="1">
            <telerik:RadBorder.GestureRecognizers>
                <TapGestureRecognizer
                    Command="{Binding ChangeModeCommand, Source={x:Reference this}}" />
            </telerik:RadBorder.GestureRecognizers>
            <Grid
                ColumnDefinitions="Auto,Auto"
                Padding="{ui:EdgeInsets All={x:Static ui:Dimens.Spacing14}}">
                <telerik:RadBorder
                    Style="{x:Static ui:Styles.MaskOnOffSwitchModeBorder}"
                    Grid.Column="0">
                    <Label
                        Style="{x:Static ui:Styles.MaskOnOffSwitchModeLabel}"
                        Text="{x:Static resources:AppResources.On}" />
                    <telerik:RadBorder.Triggers>
                        <DataTrigger
                            Binding="{Binding IsMaskMode, Source={x:Reference this}}"
                            TargetType="telerik:RadBorder"
                            Value="False">
                            <Setter
                                Property="BackgroundColor"
                                Value="{Static ui:AppColors.Transparent}" />
                        </DataTrigger>
                    </telerik:RadBorder.Triggers>
                    <telerik:RadBorder.GestureRecognizers>
                        <TapGestureRecognizer
                            Command="{Binding ChangeModeCommand, Source={x:Reference this}}" />
                    </telerik:RadBorder.GestureRecognizers>
                </telerik:RadBorder>
                <telerik:RadBorder
                    Style="{x:Static ui:Styles.MaskOnOffSwitchModeBorder}"
                    Grid.Column="1">
                    <Label
                        Style="{x:Static ui:Styles.MaskOnOffSwitchModeLabel}"
                        Text="{x:Static resources:AppResources.Off}" />
                    <telerik:RadBorder.Triggers>
                        <DataTrigger
                            Binding="{Binding IsMaskMode, Source={x:Reference this}}"
                            TargetType="telerik:RadBorder"
                            Value="True">
                            <Setter
                                Property="BackgroundColor"
                                Value="{Static ui:AppColors.Transparent}" />
                        </DataTrigger>
                    </telerik:RadBorder.Triggers>
                    <telerik:RadBorder.GestureRecognizers>
                        <TapGestureRecognizer
                            Command="{Binding ChangeModeCommand, Source={x:Reference this}}" />
                    </telerik:RadBorder.GestureRecognizers>
                </telerik:RadBorder>
            </Grid>
        </telerik:RadBorder>
    </Grid>
</telerik:RadBorder>
