<?xml version="1.0" encoding="utf-8" ?>
<telerik:RadBorder
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.BackButtonView"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    Padding="{ui:EdgeInsets All={x:Static ui:Dimens.SpacingSm2}}"
    Style="{x:Static ui:Styles.BackBorderStyle}"
    x:Name="this">
    <telerik:RadBorder.GestureRecognizers>
        <TapGestureRecognizer
            Command="{Binding BackCommand, Source={x:Reference this}}" />
    </telerik:RadBorder.GestureRecognizers>
    <VerticalStackLayout
        HorizontalOptions="Center"
        VerticalOptions="Center">
        <Image
            Source="{x:Static ui:Icons.BackIcon}"
            HeightRequest="{x:Static ui:Dimens.Height32}"
            WidthRequest="{x:Static ui:Dimens.Width32}" />
        <Label
            Style="{x:Static ui:Styles.LabelBackStyle}" />
    </VerticalStackLayout>
</telerik:RadBorder>
