<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.LoginUserView"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    x:Name="this">
    <Grid
        IsVisible="{Binding IsVisible, Source={x:Reference this}}"
        HorizontalOptions="End"
        VerticalOptions="Center"
        Margin="{ui:EdgeInsets 
            Right={x:Static ui:Dimens.SpacingS}}"
        RowSpacing="{x:Static ui:Dimens.SpacingXs}"
        RowDefinitions="auto, 1"
        ColumnDefinitions="*, *, *, *">
        <HorizontalStackLayout
            Margin="{ui:EdgeInsets 
                Right={x:Static ui:Dimens.Spacing20}}"
            Grid.RowSpan="1"
            Grid.ColumnSpan="4">
            <Label
                Style="{x:Static ui:Styles.NotoSansThinStyle}"
                Text="{x:Static resources:AppResources.LoginIdLabel}"
                FontSize="{x:Static ui:Dimens.FontSizeT7}"
                Padding="{ui:EdgeInsets 
                    Right={x:Static ui:Dimens.SpacingXl}}"
                VerticalTextAlignment="Center" />
            <Label
                Style="{x:Static ui:Styles.NotoSansThinStyle}"
                Text="{Binding LoginId, Source={x:Reference this}}"
                FontSize="{x:Static ui:Dimens.FontSizeT5}"
                VerticalTextAlignment="Center" />
        </HorizontalStackLayout>
        <BoxView
            Grid.Row="1"
            Grid.Column="1"
            Grid.ColumnSpan="3"
            BackgroundColor="{x:Static ui:AppColors.LightSlateBlue}" />
    </Grid>
</ContentView>
