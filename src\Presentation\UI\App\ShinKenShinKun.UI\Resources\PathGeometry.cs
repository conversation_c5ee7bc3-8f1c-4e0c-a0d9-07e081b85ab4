namespace ShinKenShinKun.UI;

public static class PathGeometry
{
    /// <summary>
    /// Arrow pointing left path geometry
    /// </summary>
    public static string ArrowLeft => "M15.41,16.58L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.58Z";
    
    /// <summary>
    /// Arrow pointing right path geometry
    /// </summary>
    public static string ArrowRight => "M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z";
    
    /// <summary>
    /// Arrow pointing up path geometry
    /// </summary>
    public static string ArrowUp => "M7.41,15.41L12,10.83L16.59,15.41L18,14L12,8L6,14L7.41,15.41Z";
    
    /// <summary>
    /// Arrow pointing down path geometry
    /// </summary>
    public static string ArrowDown => "M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z";
    
    /// <summary>
    /// Close/X icon path geometry
    /// </summary>
    public static string Close => "M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z";
    
    /// <summary>
    /// Check/Checkmark icon path geometry
    /// </summary>
    public static string Check => "M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z";
}
