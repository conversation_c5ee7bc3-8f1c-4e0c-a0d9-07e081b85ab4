﻿namespace ShinKenShinKun.UI;

public partial class Styles
{
    public static Style ImageStyle => CreateStyle<Image>()
        .Set(Image.AspectProperty, Aspect.AspectFit)
        .Set(Image.VerticalOptionsProperty, LayoutOptions.Center)
        .Set(Image.HorizontalOptionsProperty, LayoutOptions.Center);

    public static Style IdInputCloseButtonStyle => CreateStyle<Image>()
         .Set(Image.WidthRequestProperty, Dimens.IconSize30)
         .Set(Image.HeightRequestProperty, Dimens.IconSize30)
         .Set(Image.AspectProperty, Aspect.AspectFit)
         .Set(Image.VerticalOptionsProperty, LayoutOptions.Start)
         .Set(Image.HorizontalOptionsProperty, LayoutOptions.End)
         .Set(VisualElement.BackgroundColorProperty, Colors.Transparent);
}
