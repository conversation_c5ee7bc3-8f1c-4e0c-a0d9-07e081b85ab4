﻿namespace ShinKenShinKun;

public partial class PatientPendingPageViewModel(
    IAppNavigator appNavigator,
    IAppSettingServices appSettingServices,
    IFileSystemService fileSystemService,
    ISessionServices sessionServices,
    IMapper mapper
) : NavigationAwareBaseViewModel(appNavigator)
{
    #region Field

    private bool isBulkCheckClientInformation;
    private bool isBulkCheckMedicalChecklist;
    private bool isChangingClientInformationChecked;
    private bool isChangingMedicalChecklistChecked;
    private Dictionary<MedicalState, MedicalCheckProgressSetting> _medicalDictionary =
        appSettingServices.MedicalCheckSettingDictionary();

    #endregion Field

    #region Property

    public bool IsLogin => appSettingServices.AppSettings.IsLogin;

    #endregion Property

    #region ObservableProperty

    [ObservableProperty]
    private ObservableCollection<ClientInformationCheckedModel> clientInformations =
    [
        .. fileSystemService
            .ReadClientExcelFile()
            .Select(x => new ClientInformationCheckedModel
            {
                ClientInformation = mapper.Map<ClientInformationModel>(x),
                IsChecked = false,
            }),
    ];

    [ObservableProperty]
    private string currentUser = AppConstants.CurrentUser;

    [ObservableProperty]
    private bool isMaskMode = sessionServices.IsMaskMode;

    [ObservableProperty]
    private bool isSelectAllClient;

    [ObservableProperty]
    private bool isSelectAllMedicalChecklist;

    [ObservableProperty]
    private ObservableCollection<MedicalCheckListCheckedModel> medicalCheckLists =
    [
        .. AppConstants
            .MedicalCheckNames.Select(x => new MedicalCheckListCheckedModel
            {
                MedicalChecklist = new MedicalChecklist { Name = x },
                IsChecked = false,
            })
            .Take(16),
    ];

    [ObservableProperty]
    private string testStaff = AppConstants.TestStaff;

    #endregion ObservableProperty

    #region Private Method

    private void UpdateProgess()
    {
        foreach(var client in ClientInformations)
        {
            client.ClientInformation.MedicalCheckProgressesSubDisplays =
            [
                .. client.ClientInformation.MedicalCheckProgressesDisplays,
            ];
            foreach(var item in client.ClientInformation.MedicalCheckProgressesSubDisplays)
            {
                item.TestStatus.ProgressColor = _medicalDictionary[
                    item.TestStatus.MedicalState
                ].BackgroundColor;
            }
        }
    }

    #endregion Private Method

    #region Override Method

    public override async Task OnAppearingAsync()
    {
        await base.OnAppearingAsync();
        UpdateProgess();
    }

    protected override async Task BackAsync()
    {
        await base.BackAsync();
    }

    #endregion Override Method

    #region Command

    [RelayCommand]
    private void ChangeMode()
    {
        IsMaskMode = !IsMaskMode;
        sessionServices.IsMaskMode = IsMaskMode;
    }

    [RelayCommand]
    private void CheckAllClient()
    {
        if(isChangingClientInformationChecked)
        {
            return;
        }

        isBulkCheckClientInformation = true;
        foreach(var item in ClientInformations)
        {
            item.IsChecked = IsSelectAllClient;
        }
        isBulkCheckClientInformation = false;
    }

    [RelayCommand]
    private void CheckAllMedicalChecklist()
    {
        if(isChangingMedicalChecklistChecked)
        {
            return;
        }

        isBulkCheckMedicalChecklist = true;
        foreach(var item in MedicalCheckLists)
        {
            item.IsChecked = IsSelectAllMedicalChecklist;
        }
        isBulkCheckMedicalChecklist = false;
    }

    [RelayCommand]
    private void CheckClientInformation()
    {
        if(isBulkCheckClientInformation)
        {
            return;
        }

        isChangingClientInformationChecked = true;
        IsSelectAllClient = ClientInformations.All(x => x.IsChecked);
        isChangingClientInformationChecked = false;
    }

    [RelayCommand]
    private void CheckMedicalChecklist()
    {
        if(isBulkCheckMedicalChecklist)
        {
            return;
        }

        isChangingMedicalChecklistChecked = true;
        IsSelectAllMedicalChecklist = MedicalCheckLists.All(x => x.IsChecked);
        isChangingMedicalChecklistChecked = false;
    }

    [RelayCommand]
    private async Task GoHome()
    {
        await AppNavigator.NavigateAsync(RouterName.HomePage);
    }

    #endregion Command
}