﻿namespace ShinKenShinKun.UI;

public class AppColors
{
    public static readonly Color White = Color.FromArgb("#FFFFFFFF");
    public static readonly Color Black = Color.FromArgb("#000000");
    public static readonly Color Purple = Color.FromArgb("#FF575FCC");
    public static readonly Color Pink = Color.FromArgb("#FFFF5C9E");
    public static readonly Color Green = Color.FromArgb("#FF68D1A2");
    public static readonly Color Red = Color.FromArgb("#FFD32814");
    public static readonly Color Blue = Color.FromArgb("#6495ED");
    public static readonly Color Grey = Color.FromArgb("#989898");
    public static readonly Color DarkPastelBlue = Color.FromArgb("#7597D1");
    public static readonly Color HanBlue = Color.FromArgb("#4472C4");
    public static readonly Color LightCobaltBlue = Color.FromArgb("#8FAADC");
    public static readonly Color LightSteelBlue = Color.FromArgb("#B4C7E7");
    public static readonly Color Platinum = Color.FromArgb("#DEE4E7");
    public static readonly Color PopBackground = Black.WithAlpha(0.25f);
    public static readonly Color Transparent = Color.FromRgba(0, 0, 0, 0);
    public static readonly Color HeaderColor = Color.FromArgb("#8faadc");
    public static readonly Color LightBlue = Color.FromArgb("#87A9D4");
    public static readonly Color LightBlue2 = Color.FromArgb("#b4c7e7");
    public static readonly Color LightBlue3 = Color.FromArgb("#5766c1");
    public static readonly Color DarkBlue = Color.FromArgb("#203864");
    public static readonly Color LightGray = Color.FromArgb("#d3d3d3");
    public static readonly Color LightBlueTitle = Color.FromArgb("#4472c4");
    public static readonly Color MediumSlateBlue = Color.FromArgb("#5A65BE");
    public static readonly Color GrayBack = Color.FromArgb("#595959");
    public static readonly Color DarkGray = Color.FromArgb("#404040");
    public static readonly Color DarkSlateBlue = Color.FromArgb("#414A92");
    public static readonly Color ContentBackgroud = Color.FromArgb("#d6eaeb");
    public static readonly Color SteelBlue = Color.FromArgb("#304775");
    public static readonly Color LightSkyBlue = Color.FromArgb("#e1eaf5");
    public static readonly Color RoyalBlue = Color.FromArgb("#4971c1");
    public static readonly Color DarkLightBlueGray = Color.FromArgb("#eaeef8");
    public static readonly Color TightBlue = Color.FromArgb("#87a8ce");
    public static readonly Color Grey40 = Color.FromArgb("#FF9393AB");
    public static readonly Color Grey60 = Color.FromArgb("#FF595959");
    public static readonly Color LightPeriwinkle = Color.FromArgb("#B9C4E4");
    public static readonly Color LightSlateBlue = Color.FromArgb("#A3B7DF");
    public static readonly Color LightGrayish = Color.FromArgb("#D0CECE");
    public static readonly Color Gray82 = Color.FromArgb("#d1d1d1");
    public static readonly Color WhisperGray = Color.FromArgb("#e7e6e6");
    public static readonly Color MidnightExpressBlue = Color.FromArgb("#1d2a45");
    public static readonly Color BackdropGray = Color.FromArgb("#66000000");
    public static readonly Color DesaturatedBlue = Color.FromArgb("#688dc4");
    public static readonly Color LightBlack = Color.FromArgb("#2c2c2c");
    public static readonly Color LightRed = Color.FromArgb("#fa5850");
    public static readonly Color DarkGrey2 = Color.FromArgb("#8d8d8d");
    public static readonly Color LightGrey = Color.FromArgb("#bfbfbf");
    public static readonly Color Charcoal = Color.FromArgb("#7281b3");

    public static readonly Color DarkSlateGray = Color.FromArgb("#475169");
    public static readonly Color SlateGray = Color.FromArgb("#747474");
    public static readonly Color OnSecondary = Color.FromArgb("#FFFFFF");
    public static readonly Color Gray300 = Color.FromArgb("#ACACAC");
    public static readonly Color Gray600 = Color.FromArgb("#404040");
    public static readonly Color MediumLightBlue = Color.FromArgb("#507cc4");
    public static readonly Color PaleCerulean = Color.FromArgb("#9DC3E6");
    public static readonly Color LightOrange = Color.FromArgb("#FFC000");
    public static readonly Color LightNormalGray = Color.FromArgb("#7F7F7F");
    public static readonly Color LightNormalOrange = Color.FromArgb("#4472C4");
    public static readonly Color LightPeriwinkleBlue = Color.FromArgb("#B8C3E6");
    public static readonly Color AzureishWhite = Color.FromArgb("#E0F3F1");
    public static readonly Color AzureishWhiteBlue = Color.FromArgb("#DCEAF7");
    public static readonly Color SilverFoil = Color.FromArgb("#AEAEAE");
    public static readonly Color LightGrayishBlue = Color.FromArgb("#AFB7C9");
    public static readonly Color DarkIndigo = Color.FromArgb("#2A3163");
    public static readonly Color MediumGray = Color.FromArgb("#767171");
    public static readonly Color GoldenYellow = Color.FromArgb("#F7C92E");
    public static readonly Color BrightRed = Color.FromArgb("#F92F1A");
    public static readonly Color LightGrayishWhite = Color.FromArgb("#E8E8E8");
    public static readonly Color LightGrayishGray = Color.FromArgb("#A6A6A6");
    public static readonly Color BrightMagenta = Color.FromArgb("#CF37BC");
    public static readonly Color MediumBlue = Color.FromArgb("#5766C1");
    public static readonly Color MediumLightGray = Color.FromArgb("#AEAEAE");
    public static readonly Color CyanCornflowerBlue = Color.FromArgb("#2684C6");
    public static readonly Color DeepTealBlue = Color.FromArgb("#156082");
    public static readonly Color BackgroundColorPopup = Color.FromArgb("#33999999");
    public static readonly Color BorderColorPopup = Color.FromArgb("#a8a8a8");
    public static readonly Color SpaceCadet = Color.FromArgb("#12175c");
    public static readonly Color SoftGray = Color.FromArgb("#D9D9D9");
    public static readonly Color AlloyOrange = Color.FromArgb("#c3591e");
    public static readonly Color VividRed = Color.FromArgb("#fa0019");
    public static readonly Color SlimyGreen = Color.FromArgb("#199a14");
    public static readonly Color TeaGreen = Color.FromArgb("#c2ffc2");
    public static readonly Color VeryPaleYellow = Color.FromArgb("#feffc2");
    public static readonly Color SeaGreen = Color.FromArgb("#338d56");
    public static readonly Color RipeMango = Color.FromArgb("#fcc028");
    public static readonly Color UltramarineBlue = Color.FromArgb("#3163fc");
    public static readonly Color Saffron = Color.FromArgb("#fcc135");
    public static readonly Color SunburntCyclops = Color.FromArgb("#fa444d");
    public static readonly Color CobaltBlue = Color.FromArgb("#0259fa");
}