﻿namespace ShinKenShinKun;

public partial class ExamineBeforeAuthViewModel(
    IAppNavigator appNavigator,
    IAppSettingServices appSettingServices,
    IExamineService examineService,
    IBarcodeScannerServices barcodeListenerServices,
    IDispatcher dispatcher,
    ISessionServices sessionServices) : NavigationAwareBaseViewModel(appNavigator)
{
    #region Field

    private bool isShowNoteEditPopup;

    #endregion

    #region ObservableProperty

    [ObservableProperty] private ObservableCollection<ClientInformationModel> clientInformations;
    [ObservableProperty] private ClientInformationModel? clientSelected;
    [ObservableProperty] private ObservableCollection<StayFlagModel> guidedFilters;
    [ObservableProperty] private StayFlagModel guidedSelected;
    [ObservableProperty] private ObservableCollection<MedicalCheckModel> medicalCheckFilters;
    [ObservableProperty] private MedicalCheckModel medicalCheckSelected;
    [ObservableProperty] private string medicalMachineName;
    [ObservableProperty] private bool isMaskMode;
    [ObservableProperty] private bool isPlayGuideMode = true;
    [ObservableProperty] private bool isLogin;
    [ObservableProperty] private string loginId;
    [ObservableProperty] private bool isBackToHome = false;
    [ObservableProperty] private ScrollPositionModel noteScroll;
    [ObservableProperty] private ScrollPositionModel personalNoteScroll;

    #endregion

    #region RelayCommand

    [RelayCommand]
    private async Task GuideModeTransfer()
    {
        var message = IsPlayGuideMode
            ? AppResources.GuideStopDialogTitle
            : AppResources.GuideOpenDialogTitle;
        if(await AppNavigator.ShowConfirmationDialog($"{MedicalMachineName + message}"))
        {
            IsPlayGuideMode = !IsPlayGuideMode;
        }
    }

    [RelayCommand]
    private void ChangeMode()
    {
        IsMaskMode = !IsMaskMode;
        sessionServices.IsMaskMode = IsMaskMode;
    }

    [RelayCommand]
    private async Task Reserve()
    {
        if(ClientSelected == null)
        {
            return;
        }
        var reserveState = ClientSelected.ReserveState?.Trim();
        if(string.IsNullOrEmpty(reserveState))
        {
            var isDeviceReservatingOtherClient = ClientInformations.Any(c =>
                 c.ReserveState?.Trim().Equals(MedicalMachineName) ?? false
            );

            if(isDeviceReservatingOtherClient)
            {
                await AppNavigator.ShowNotificationDialog(AppResources.ReserveNoConfirmTitle);
            }
            else
            {
                ClientSelected.ReserveState = MedicalMachineName;
            }
        }
        else
        {
            if(reserveState.Equals(MedicalMachineName?.Trim()))
            {
                if(await AppNavigator.ShowConfirmationDialog(AppResources.ReserveCancelTitle))
                {
                    ClientSelected.ReserveState = string.Empty;
                }
            }
            else
            {
                await AppNavigator.ShowNotificationDialog(AppResources.ReserveNoCanceltitle);
            }
        }
    }

    [RelayCommand]
    private void Update()
    {
        ClientInformations = examineService.LoadDataClientInformations(GuidedSelected, MedicalCheckSelected);
        ClientSelected = ClientInformations?.FirstOrDefault();
    }

    [RelayCommand]
    private async Task GoHome()
    {
        await AppNavigator.NavigateAsync(RouterName.HomePageRoot);
    }

    [RelayCommand]
    private async Task ShowNoteEditPopup()
    {
        if(ClientSelected != null)
        {
            var popup = new NoteEditPopup();
            popup.Note = ClientSelected.Note;
            popup.Opened += (s, e) =>
            {
                isShowNoteEditPopup = true;
            };
            popup.Closed += (s, e) =>
            {
                isShowNoteEditPopup = false;
            };
            await AppNavigator.ShowPopupAsync(popup);
        }
    }

    [RelayCommand]
    private async Task ShowInputClientIdPopup()
    {
        var popup = new IdInputPopup
        {
            Title = AppResources.InputClientIdButton
        };
        popup.ConfirmCommand = new Command(async () =>
        {
            await ConfirmInputClientIdPopup(popup.InputValue);
            popup.Close();
        });
        await AppNavigator.ShowPopupAsync(popup);
    }

    [RelayCommand]
    private async Task ConfirmInputClientIdPopup(string clientId)
    {
        var id = clientId;
        if(!string.IsNullOrEmpty(id))
        {
            await GoToExaminaAfterAuth();
        }
    }

    [RelayCommand]
    private async Task GoToPendingTestSelection()
    {
        if(ClientSelected == null)
        {
            return;
        }
        await AppNavigator.NavigateAsync(RouterName.PendingTestSelectionPage, false, ClientSelected);
    }

    [RelayCommand]
    private async Task GoToStopTestSelection()
    {
        if(ClientSelected == null)
        {
            return;
        }
        await AppNavigator.NavigateAsync(RouterName.StopTestSelectionPage, false, ClientSelected);
    }

    [RelayCommand]
    private void ResetScroll()
    {
        sessionServices.NoteViewScroll.ScrollY = 0;
        sessionServices.PersonalNoteViewScroll.ScrollY = 0;
    }

    #endregion RelayCommand

    #region Override Method

    partial void OnMedicalCheckSelectedChanged(MedicalCheckModel? oldValue, MedicalCheckModel newValue)
    {
        if(oldValue == null || MedicalCheckSelected == null || GuidedSelected == null)
        {
            return;
        }
        ClientInformations = examineService.LoadDataClientInformations(GuidedSelected, MedicalCheckSelected);
        ClientSelected = ClientInformations?.FirstOrDefault();
    }

    partial void OnGuidedSelectedChanged(StayFlagModel? oldValue, StayFlagModel newValue)
    {
        if(oldValue == null || MedicalCheckSelected == null || GuidedSelected == null)
        {
            return;
        }
        ClientInformations = examineService.LoadDataClientInformations(GuidedSelected, MedicalCheckSelected);
        ClientSelected = ClientInformations?.FirstOrDefault();
    }

    protected override void OnInit(IDictionary<string, object> query)
    {
        base.OnInit(query);
        var data = query.GetData<Tuple<ClientInformationModel, string, bool>>();
        if(data != null)
        {
            ClientSelected = data.Item1;
            MedicalMachineName = data.Item2 ?? appSettingServices.AppSettings.DefaultMedicalCheck;
            IsBackToHome = data.Item3;
        }
        if(string.IsNullOrEmpty(MedicalMachineName))
        {
            MedicalMachineName = query.GetData<string>() ?? appSettingServices.AppSettings.DefaultMedicalCheck;
        }
    }

    protected override void OnBack(IDictionary<string, object> query)
    {
        NoteScroll = sessionServices.NoteViewScroll;
        PersonalNoteScroll = sessionServices.PersonalNoteViewScroll;
        if(string.IsNullOrEmpty(MedicalMachineName))
        {
            MedicalMachineName = query.GetData<string>() ?? appSettingServices.AppSettings.DefaultMedicalCheck;
        }
    }
    public override Task OnAppearingAsync()
    {
        sessionServices.NoteViewScroll.ScrollY = 0;
        sessionServices.PersonalNoteViewScroll.ScrollY = 0;
        NoteScroll = sessionServices.NoteViewScroll;
        PersonalNoteScroll = sessionServices.PersonalNoteViewScroll;
        GuidedFilters = examineService.GetGuidedDisplayFilterList();
        MedicalCheckFilters = examineService.GetMedicalCheckDisplayFilterList();
        IsLogin = appSettingServices.AppSettings.IsLogin;
        LoginId = sessionServices.LoginId;
        IsMaskMode = sessionServices.IsMaskMode;
        GuidedSelected = GuidedFilters.First();
        MedicalCheckSelected = MedicalCheckFilters.First();
        barcodeListenerServices.OnBarcodeScanned += OnBarcodeScanned;
        return Task.Run(async () =>
        {
            await Task.Delay(500);
            dispatcher.Dispatch(() =>
            {
                ClientInformations = examineService.LoadDataClientInformations(GuidedSelected, MedicalCheckSelected);
                ClientSelected = ClientInformations?.FirstOrDefault();
            });
        });
    }

    public override Task OnDisappearingAsync()
    {
        sessionServices.NoteViewScroll = NoteScroll;
        sessionServices.PersonalNoteViewScroll = PersonalNoteScroll;
        barcodeListenerServices.OnBarcodeScanned -= OnBarcodeScanned;
        return base.OnDisappearingAsync();
    }

    protected override async Task BackAsync()
    {
        if(string.IsNullOrEmpty(appSettingServices.AppSettings.DefaultMedicalCheck) && IsBackToHome == false)
        {
            await AppNavigator.GoBackAsync();
        }
        else
        {
            await AppNavigator.NavigateAsync($"{RouterName.HomePageRoot}");
        }
    }

    #endregion Override Method

    #region PublicMethod

    public void StartListening()
    {
        barcodeListenerServices.StartListening();
    }

    public void StopListening()
    {
        barcodeListenerServices.StopListening();
    }

    #endregion PublicMethod

    #region Private Method

    private async void OnBarcodeScanned(string obj)
    {
        if(string.IsNullOrEmpty(obj) || ClientSelected == null || isShowNoteEditPopup)
        {
            return;
        }
        await GoToExaminaAfterAuth();
    }

    private async Task GoToExaminaAfterAuth()
    {
        if (ClientSelected == null)
        {
            return;
        }
        Tuple<ClientInformationModel, string> tuple = new(ClientSelected, MedicalMachineName);
        await appNavigator.NavigateAsync(nameof(RouterName.ExamineAfterAuthPage), false, tuple);
    }

    #endregion Private Method
}