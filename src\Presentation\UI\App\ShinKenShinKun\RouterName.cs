﻿namespace ShinKenShinKun;

public class RouterName
{
    public const string LoginPage = "LoginPage";
    public const string HomePage = "HomePage";
    public const string HomePageRoot = "//HomePage";
    public const string TestSelectionPage = "TestSelectionPage";
    public const string ExamineBeforeAuthPage = "ExamineBeforeAuthPage";
    public const string GuidanceSelectionPage = "GuidanceSelectionPage";
    public const string GuidanceSettingPage = "GuidanceSettingPage";
    public const string ExamineAfterAuthPage = "ExamineAfterAuthPage";
    public const string IndividualProgressPage = "IndividualProgressPage";
    public const string PatientPendingPage = "PatientPendingPage";
    public const string PatientGuidanceAdjustmentPage = "GuidanceAdjustmentPage";
    public const string PendingTestSelectionPage = "PendingTestSelectionPage";
    public const string StopExaminePage = "StopExaminePage";
    public const string StopTestSelectionPage = "StopTestSelectionPage";
    public const string BlankPage = "//Login/BlankPage";
    public const string SelectCheckPage = "SelectCheckPage";
}
