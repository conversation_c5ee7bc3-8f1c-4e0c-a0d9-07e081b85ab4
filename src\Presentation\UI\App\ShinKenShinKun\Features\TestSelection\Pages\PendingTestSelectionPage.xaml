<?xml version="1.0" encoding="utf-8" ?>
<mvvm:BasePage
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.PendingTestSelectionPage"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    x:DataType="app:PendingTestSelectionViewModel"
    xmlns:app="clr-namespace:ShinKenShinKun">
    <Shell.BackButtonBehavior>
        <BackButtonBehavior
            IsEnabled="False"
            IsVisible="False" />
    </Shell.BackButtonBehavior>

    <Grid
        BackgroundColor="{x:Static ui:AppColors.HeaderColor}"
        RowDefinitions="80, *, Auto">
        <!--Header-->
        <Grid
            Grid.Row="0"
            ColumnDefinitions="*, Auto, *">
            <app:MaskButtonView
                Grid.Column="0"
                HorizontalOptions="Start"
                VerticalOptions="Center"
                Margin="{ui:EdgeInsets Left={x:Static ui:Dimens.Spacing122}}"
                ChangeModeCommand="{Binding ChangeModeCommand}"
                IsMaskMode="{Binding IsMaskMode}" />
            <Label
                Grid.Column="1"
                HorizontalOptions="Center"
                Text="{x:Static resources:AppResources.SelectionPendingTitle}"
                Style="{Static ui:Styles.AppHeaderLabelStyle}" />
            <app:LoginUserView
                IsVisible="{Binding IsLogin}"
                LoginId="{Binding LoginId}"
                Grid.Column="2" />
        </Grid>

        <!--Content-->
        <Grid
            Grid.Row="1"
            ColumnDefinitions="3*,7*"
            BackgroundColor="{x:Static ui:AppColors.ContentBackgroud}">
            <app:PatientInfoView
                NoteScroll="{Binding NoteScroll}"
                PersonalNoteScroll="{Binding PersonalNoteScroll}"
                ShowNoteEditPopupCommand="{Binding ShowNoteEditPopupCommand}"
                IsMaskMode="{Binding IsMaskMode}"
                IsItemSelected="{Binding ClientSelected,
                    Converter={x:Static ui:AppConverters.ObjectToBoolConverter}}"
                ItemSelected="{Binding ClientSelected}"
                Grid.Column="0" />
            <telerik:RadBorder
                Style="{x:Static ui:Styles.FrameBorderStyle}"
                Margin="{ui:EdgeInsets All={x:Static ui:Dimens.Spacing10}}"
                Grid.Column="1">
                <!--Guide Items-->
                <app:SelectionTestStateListView
                    MedicalCheckProgresses="{Binding MedicalCheckProgresses}"
                    SelectionChangedCommand="{Binding SelectButtonCommand}" />
            </telerik:RadBorder>
        </Grid>

        <!--Footer-->
        <Grid
            BackgroundColor="{x:Static ui:AppColors.LightCobaltBlue}"
            Padding="{x:Static ui:Dimens.SpacingSm2}"
            HeightRequest="{x:Static ui:Dimens.Height100}"
            Grid.Row="2"
            ColumnDefinitions="Auto,*,Auto">
            <app:BackButtonView
                BackCommand="{Binding BackCommand}"
                Grid.Column="0" />
            <HorizontalStackLayout
                Grid.Column="2"
                Spacing="{x:Static ui:Dimens.SpacingMd}">
                <telerik:RadBorder
                    Style="{x:Static ui:Styles.PendingTestAllBorderStyle}">
                    <VerticalStackLayout
                        HorizontalOptions="Center"
                        VerticalOptions="Center">
                        <Image
                            Aspect="Fill"
                            Source="{x:Static ui:Icons.PauseCircle}"
                            HeightRequest="{x:Static ui:Dimens.SpacingXl}"
                            WidthRequest="{x:Static ui:Dimens.SpacingXl}" />
                        <Label
                            Style="{x:Static ui:Styles.LabelPendingTestAllStyle}" />
                    </VerticalStackLayout>
                    <telerik:RadBorder.GestureRecognizers>
                        <TapGestureRecognizer
                            Command="{Binding PendingAllCommand}" />
                    </telerik:RadBorder.GestureRecognizers>
                </telerik:RadBorder>
                <telerik:RadBorder
                    Style="{x:Static ui:Styles.CancelAllBorderStyle}">
                    <VerticalStackLayout
                        HorizontalOptions="Center"
                        VerticalOptions="Center">
                        <Image
                            Aspect="Fill"
                            Source="{x:Static ui:Icons.CancelIcon}"
                            HeightRequest="{x:Static ui:Dimens.SpacingXl}"
                            WidthRequest="{x:Static ui:Dimens.SpacingXl}" />
                        <Label
                            Style="{x:Static ui:Styles.LabeCancelAllStyle}" />
                    </VerticalStackLayout>
                    <telerik:RadBorder.GestureRecognizers>
                        <TapGestureRecognizer
                            Command="{Binding CancelAllCommand}" />
                    </telerik:RadBorder.GestureRecognizers>
                </telerik:RadBorder>
                <telerik:RadBorder
                    Style="{x:Static ui:Styles.SaveButtonBorderStyle}">
                    <VerticalStackLayout
                        HorizontalOptions="Center"
                        VerticalOptions="Center">
                        <Image
                            Source="{x:Static ui:Icons.SaveIcon}"
                            HeightRequest="{x:Static ui:Dimens.SpacingXl}"
                            WidthRequest="{x:Static ui:Dimens.SpacingXl}" />
                        <Label
                            Style="{x:Static ui:Styles.SaveButtonLabelStyle}" />
                    </VerticalStackLayout>
                    <telerik:RadBorder.GestureRecognizers>
                        <TapGestureRecognizer
                            Command="{Binding BackCommand}" />
                    </telerik:RadBorder.GestureRecognizers>
                </telerik:RadBorder>
            </HorizontalStackLayout>
        </Grid>

    </Grid>
</mvvm:BasePage>