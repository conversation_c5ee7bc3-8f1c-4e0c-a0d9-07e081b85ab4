<?xml version="1.0" encoding="utf-8"?>

<mvvm:BasePage
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.ExamineBeforeAuthPage"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    x:DataType="app:ExamineBeforeAuthViewModel"
    xmlns:app="clr-namespace:ShinKenShinKun">
    <Shell.BackButtonBehavior>
        <BackButtonBehavior
            IsVisible="False"
            IsEnabled="False" />
    </Shell.BackButtonBehavior>
    <Grid
        BackgroundColor="{x:Static ui:AppColors.LightCobaltBlue}"
        RowDefinitions="80, *, Auto">
        <!--Header-->
        <Grid
            Grid.Row="0"
            ColumnDefinitions="*, Auto, *">
            <HorizontalStackLayout
                Grid.Column="0"
                VerticalOptions="Center"
                Spacing="{x:Static ui:Dimens.SpacingXl}"
                Margin="{ui:EdgeInsets Left={x:Static ui:Dimens.SpacingSm2}}">
                <app:HomeButtonView
                    GoHomeCommand="{Binding GoHomeCommand}" />
                <app:MaskButtonView
                    Grid.Column="1"
                    VerticalOptions="Center"
                    ChangeModeCommand="{Binding ChangeModeCommand}"
                    IsMaskMode="{Binding IsMaskMode}" />
            </HorizontalStackLayout>
            <HorizontalStackLayout
                Grid.Column="1"
                HorizontalOptions="Center"
                Spacing="{x:Static ui:Dimens.SpacingLg}">
                <Label
                    Text="{x:Static resources:AppResources.ExaminationScreen}"
                    Style="{Static ui:Styles.AppHeaderLabelStyle}" />
                <Label
                    Text="{Binding MedicalMachineName}"
                    Style="{Static ui:Styles.AppHeaderLabelStyle}" />
            </HorizontalStackLayout>

            <app:LoginUserView
                IsVisible="{Binding IsLogin}"
                LoginId="{Binding LoginId}"
                Grid.Column="2" />
        </Grid>

        <!--Content-->
        <Grid
            Grid.Row="1"
            ColumnDefinitions="3*,7*"
            BackgroundColor="{x:Static ui:AppColors.ContentBackgroud}">
            <app:PatientInfoView
                NoteScroll="{Binding NoteScroll}"
                PersonalNoteScroll="{Binding PersonalNoteScroll}"
                ShowNoteEditPopupCommand="{Binding ShowNoteEditPopupCommand}"
                IsMaskMode="{Binding IsMaskMode}"
                IsItemSelected="{Binding ClientSelected,
                    Converter={x:Static ui:AppConverters.ObjectToBoolConverter}}"
                ItemSelected="{Binding ClientSelected}"
                Grid.Column="0" />
            <app:PatientListMonitorView
                Grid.Column="1" />
        </Grid>

        <!--Footer-->
        <Grid
            BackgroundColor="{x:Static ui:AppColors.LightCobaltBlue}"
            Padding="{x:Static ui:Dimens.SpacingSm2}"
            HeightRequest="{x:Static ui:Dimens.Height100}"
            Grid.Row="2"
            ColumnDefinitions="Auto,*,Auto">
            <app:BackButtonView
                BackCommand="{Binding BackCommand}"
                Grid.Column="0" />
            <HorizontalStackLayout
                Grid.Column="2"
                Spacing="{x:Static ui:Dimens.SpacingMd}">
                <telerik:RadBorder
                    Style="{x:Static ui:Styles.InputClientIdBorderStyle}">
                    <VerticalStackLayout
                        HorizontalOptions="Center"
                        VerticalOptions="Center">
                        <Image
                            Aspect="Fill"
                            Source="{x:Static ui:Icons.TableIcon}"
                            HeightRequest="{x:Static ui:Dimens.SpacingXl}"
                            WidthRequest="{x:Static ui:Dimens.SpacingXl}" />
                        <Label
                            Style="{x:Static ui:Styles.LabelInputClientIdStyle}" />
                    </VerticalStackLayout>
                    <telerik:RadBorder.GestureRecognizers>
                        <TapGestureRecognizer
                            Command="{Binding ShowInputClientIdPopupCommand}" />
                    </telerik:RadBorder.GestureRecognizers>
                </telerik:RadBorder>
                <telerik:RadBorder
                    Style="{x:Static ui:Styles.PendingBorderStyle}">
                    <VerticalStackLayout
                        HorizontalOptions="Center"
                        VerticalOptions="Center">
                        <Image
                            Aspect="Fill"
                            Source="{x:Static ui:Icons.PauseCircle}"
                            HeightRequest="{x:Static ui:Dimens.SpacingXl}"
                            WidthRequest="{x:Static ui:Dimens.SpacingXl}" />
                        <Label
                            Style="{x:Static ui:Styles.LabelPendingStyle}" />
                    </VerticalStackLayout>
                    <telerik:RadBorder.GestureRecognizers>
                        <TapGestureRecognizer
                            Command="{Binding GoToPendingTestSelectionCommand}" />
                    </telerik:RadBorder.GestureRecognizers>
                </telerik:RadBorder>
                <telerik:RadBorder
                    Style="{x:Static ui:Styles.StopBorderStyle}">
                    <VerticalStackLayout
                        HorizontalOptions="Center"
                        VerticalOptions="Center">
                        <Image
                            Aspect="Fill"
                            Source="{x:Static ui:Icons.BanIcon}"
                            HeightRequest="{x:Static ui:Dimens.SpacingXl}"
                            WidthRequest="{x:Static ui:Dimens.SpacingXl}" />
                        <Label
                            Style="{x:Static ui:Styles.LabelStopStyle}" />
                    </VerticalStackLayout>
                    <telerik:RadBorder.GestureRecognizers>
                        <TapGestureRecognizer
                            Command="{Binding GoToStopTestSelectionCommand}" />
                    </telerik:RadBorder.GestureRecognizers>
                </telerik:RadBorder>
                <BoxView
                    BackgroundColor="{x:Static ui:AppColors.Transparent}"
                    WidthRequest="{x:Static ui:Dimens.Spacing200}" />
            </HorizontalStackLayout>
        </Grid>

    </Grid>
</mvvm:BasePage>