using Microsoft.Maui.Controls;
using ShinKenShinKun.UI;
using Telerik.Maui.Controls;
using System.Collections.ObjectModel;

namespace ShinKenShinKun;

public partial class CheckDetailPage : ContentPage
{
    public CheckDetailPage(CheckDetailPageViewModel viewModel)
    {
        InitializeComponent();
        BindingContext = viewModel;
        
        // Subscribe to property changes to rebuild UI when data changes
        viewModel.PropertyChanged += OnViewModelPropertyChanged;
    }

    private void OnViewModelPropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(CheckDetailPageViewModel.AreaSettings))
        {
            BuildDynamicContent();
        }
    }

    protected override void OnAppearing()
    {
        base.OnAppearing();
        BuildDynamicContent();
    }

    private void BuildDynamicContent()
    {
        var viewModel = BindingContext as CheckDetailPageViewModel;
        if (viewModel?.AreaSettings == null) return;

        // Clear existing content
        DynamicContentContainer.Children.Clear();

        foreach (var areaSetting in viewModel.AreaSettings)
        {
            var areaView = CreateAreaView(areaSetting);
            DynamicContentContainer.Children.Add(areaView);
        }
    }

    private View CreateAreaView(AreaModel areaSetting)
    {
        var border = new Border
        {
            BackgroundColor = AppColors.White,
            Stroke = AppColors.LightGray,
            StrokeThickness = 1,
            Padding = new Thickness(10),
            Margin = new Thickness(5)
        };

        var grid = CreateGridFromAreaSetting(areaSetting);
        border.Content = grid;

        return border;
    }

    private Grid CreateGridFromAreaSetting(AreaModel areaSetting)
    {
        var grid = new Grid
        {
            HeightRequest = areaSetting.Height,
            Margin = areaSetting.MarginDisplay,
            HorizontalOptions = areaSetting.HorizontalOptions,
            VerticalOptions = areaSetting.VerticalOptions
        };

        // Set up row and column definitions
        SetupGridDefinitions(grid, areaSetting.RowRatios, areaSetting.ColumnRatios);

        // Add components to grid
        foreach (var component in areaSetting.ComponentLayouts)
        {
            var view = CreateComponentView(component);
            if (view != null)
            {
                Grid.SetRow(view, component.RowIndex);
                Grid.SetColumn(view, component.ColumnIndex);
                Grid.SetRowSpan(view, component.RowSpan);
                Grid.SetColumnSpan(view, component.ColumnSpan);
                grid.Children.Add(view);
            }
        }

        return grid;
    }

    private void SetupGridDefinitions(Grid grid, string rowRatios, string columnRatios)
    {
        // Parse row ratios
        if (!string.IsNullOrEmpty(rowRatios))
        {
            var rows = rowRatios.Split(',');
            foreach (var row in rows)
            {
                if (double.TryParse(row.Trim(), out double ratio))
                {
                    grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(ratio, GridUnitType.Star) });
                }
                else
                {
                    grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
                }
            }
        }

        // Parse column ratios
        if (!string.IsNullOrEmpty(columnRatios))
        {
            var columns = columnRatios.Split(',');
            foreach (var column in columns)
            {
                if (double.TryParse(column.Trim(), out double ratio))
                {
                    grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(ratio, GridUnitType.Star) });
                }
                else
                {
                    grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
                }
            }
        }
    }

    private View CreateComponentView(ShinKenShinKun.DataAccess.ComponentLayoutCombineModel component)
    {
        return (int)component.ComponentType switch
        {
            0 => CreateLabel(component),
            1 => CreateEntry(component),
            2 => CreateButton(component),
            3 => CreateDisplayLabel(component),
            4 => CreateStatusButton(component),
            5 => CreateActionButton(component),
            _ => new Label { Text = "Unknown Component" }
        };
    }

    private Label CreateLabel(ShinKenShinKun.DataAccess.ComponentLayoutCombineModel component)
    {
        var label = new Label
        {
            Text = component.Text,
            Margin = component.MarginDisplay,
            HorizontalOptions = component.HorizontalOptions,
            VerticalOptions = component.VerticalOptions,
            TextColor = Colors.Black
        };

        return label;
    }

    private Entry CreateEntry(ShinKenShinKun.DataAccess.ComponentLayoutCombineModel component)
    {
        var entry = new Entry
        {
            Placeholder = component.Title,
            Text = component.Value,
            Margin = component.MarginDisplay,
            HorizontalOptions = component.HorizontalOptions,
            VerticalOptions = component.VerticalOptions
        };

        return entry;
    }

    private RadButton CreateButton(ShinKenShinKun.DataAccess.ComponentLayoutCombineModel component)
    {
        var button = new RadButton
        {
            Text = component.Text,
            Margin = component.MarginDisplay,
            HorizontalOptions = component.HorizontalOptions,
            VerticalOptions = component.VerticalOptions
        };

        return button;
    }

    private Label CreateDisplayLabel(ShinKenShinKun.DataAccess.ComponentLayoutCombineModel component)
    {
        var label = new Label
        {
            Text = $"{component.Title}: {component.Value}",
            Margin = component.MarginDisplay,
            HorizontalOptions = component.HorizontalOptions,
            VerticalOptions = component.VerticalOptions,
            TextColor = Colors.Black
        };

        return label;
    }

    private RadButton CreateStatusButton(ShinKenShinKun.DataAccess.ComponentLayoutCombineModel component)
    {
        var button = new RadButton
        {
            Text = component.CurrentState?.StatusName ?? component.Text,
            Margin = component.MarginDisplay,
            HorizontalOptions = component.HorizontalOptions,
            VerticalOptions = component.VerticalOptions,
            BackgroundColor = component.CurrentState?.StatusColor ?? Colors.LightGray,
            TextColor = Colors.Black
        };

        // Add tap gesture to cycle through status options
        if (component.StatusList?.Count > 0)
        {
            var tapGesture = new TapGestureRecognizer();
            tapGesture.Tapped += (s, e) => CycleStatus(button, component);
            button.GestureRecognizers.Add(tapGesture);
        }

        return button;
    }

    private RadButton CreateActionButton(ShinKenShinKun.DataAccess.ComponentLayoutCombineModel component)
    {
        var button = new RadButton
        {
            Text = component.Text,
            Margin = component.MarginDisplay,
            HorizontalOptions = component.HorizontalOptions,
            VerticalOptions = component.VerticalOptions,
            BackgroundColor = Colors.Blue,
            TextColor = Colors.White
        };

        return button;
    }

    private void CycleStatus(RadButton button, ShinKenShinKun.DataAccess.ComponentLayoutCombineModel component)
    {
        if (component.StatusList == null || component.StatusList.Count == 0) return;

        var currentIndex = -1;
        for (int i = 0; i < component.StatusList.Count; i++)
        {
            if (component.StatusList[i].StatusName == component.CurrentState?.StatusName)
            {
                currentIndex = i;
                break;
            }
        }

        var nextIndex = (currentIndex + 1) % component.StatusList.Count;
        var nextStatus = component.StatusList[nextIndex];

        component.CurrentState = nextStatus;
        button.Text = nextStatus.StatusName;
        button.BackgroundColor = nextStatus.StatusColor;
        button.TextColor = Colors.Black;
    }


}
