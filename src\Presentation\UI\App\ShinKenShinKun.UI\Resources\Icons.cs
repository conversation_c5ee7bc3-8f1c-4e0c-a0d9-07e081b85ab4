﻿namespace ShinKenShinKun.UI;

public class Icons
{
    public static readonly string AddIcon = "add_icon.png";
    public static readonly string ArrowChevronRightIcon = "icon_arrow_chevron_right.png";
    public static readonly string ArrowCircleLeft24x24 = "arrow_circle_left_24x24.png";
    public static readonly string ArrowCircleRight24x24 = "arrow_circle_right_24x24.png";
    public static readonly string ArrowdownIcon = "arrow_down.png";
    public static readonly string BackDoorIcon = "back_door_icon.png";
    public static readonly string BackIcon = "back_icon.png";
    public static readonly string BanIcon = "ban_icon.png";
    public static readonly string BulkHoldScreen = "bulkholdscreen.png";
    public static readonly string CancelIcon = "cancel.png";
    public static readonly string ChangeGuideIcon = "change_guide.png";
    public static readonly string Circle24X24 = "circle_24x24.png";
    public static readonly string Close40x40 = "close_40x40.png";
    public static readonly string CloseIcon = "close_icon.png";
    public static readonly string DotnetIcon = "dotnet_bot.png";
    public static readonly string GaidancesScreen = "guidancescreen.png";
    public static readonly string Home40X40 = "home_40x40.png";
    public static readonly string IndividualIcon = "individualicon.png";
    public static readonly string Logout = "logout.png";
    public static readonly string MedicalCheckImage = "medicalcheckimage.png";
    public static readonly string PauseCircle = "pause_circle.png";
    public static readonly string PendingAllIcon = "pending_all.png";
    public static readonly string PlayCircle = "play_circle.png";
    public static readonly string PowerOff40X40 = "power_off_40x40.png";
    public static readonly string ProgressIcon = "progressicon.png";
    public static readonly string SaveIcon = "save.png";
    public static readonly string Search24X24 = "search_24x24.png";
    public static readonly string SearchIcon = "search.png";
    public static readonly string ShakeHand = "shake_hand.png";
    public static readonly string TableIcon = "table_icon.png";
    public static readonly string Undo40X40 = "undo_40x40.png";
    public static readonly string Update = "update.png";
    public static readonly string VariousSetting = "varioussettingsicon.png";
    public static readonly string Visibility = "visibility.png";
    public static readonly string VisibilityOff = "visibility_off.png";
    public static readonly string XIcon = "x_icon.png";
}