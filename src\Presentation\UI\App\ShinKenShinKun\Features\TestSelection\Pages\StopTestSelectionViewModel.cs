﻿namespace ShinKenShinKun;

public partial class StopTestSelectionViewModel(
    IAppNavigator appNavigator,
    ISessionServices sessionServices,
    IAppSettingServices appSettingServices)
    : NavigationAwareBaseViewModel(appNavigator)
{
    #region ObservableProperties
    [ObservableProperty] private ClientInformationModel? clientSelected;
    [ObservableProperty] private ObservableCollection<MedicalCheckStateModel> medicalCheckProgresses;
    [ObservableProperty] private bool isMaskMode;
    [ObservableProperty] private string loginId;
    [ObservableProperty] private bool isLogin;
    [ObservableProperty] private ScrollPositionModel noteScroll;
    [ObservableProperty] private ScrollPositionModel personalNoteScroll;
    #endregion ObservableProperties

    #region FieldProperties

    private Dictionary<MedicalState, MedicalCheckProgressSetting> _medicalDictionary;

    #endregion FieldProperties

    #region RelayCommand

    [RelayCommand]
    private void ChangeMode()
    {
        IsMaskMode = !IsMaskMode;
        sessionServices.IsMaskMode = IsMaskMode;
    }

    [RelayCommand]
    private void SelectButton(MedicalCheckStateModel dedicalCheckStateItem)
    {
        TransferStatusProcess(dedicalCheckStateItem);
    }

    [RelayCommand]
    private void StopAll()
    {
        foreach(var item in MedicalCheckProgresses
            .Where(x => new[] { MedicalState.Unchecked, MedicalState.Next, MedicalState.Pending }.Contains(x.TestStatus.MedicalState)))
        {
            item.TestStatus = new TestStatusModel
            {
                MedicalState = MedicalState.Stopped,
                DisplayName = AppResources.Stopped,
                StatusColor = AppColors.Grey40,
                TextColor = _medicalDictionary[MedicalState.Stopped].TextColor,
                ProgressColor = _medicalDictionary[MedicalState.Stopped].BackgroundColor

            };

        }
    }

    [RelayCommand]
    private void CancelAll()
    {
        foreach(var dedicalCheckStateItem in MedicalCheckProgresses.Where(x => x.TestStatus.MedicalState == MedicalState.Stopped))
        {
            dedicalCheckStateItem.TestStatus = new TestStatusModel
            {
                MedicalState = MedicalState.Unchecked,
                DisplayName = AppResources.Unchecked,
                StatusColor = AppColors.White,
                TextColor = _medicalDictionary[MedicalState.Unchecked].TextColor,
                ProgressColor = _medicalDictionary[MedicalState.Unchecked].BackgroundColor

            };
        }
    }

    [RelayCommand]
    private async Task ShowNoteEditPopup()
    {
        if (ClientSelected != null)
        {
            var popup = new NoteEditPopup();
            popup.Note = ClientSelected.Note;
            await AppNavigator.ShowPopupAsync(popup);
        }
    }

    #endregion

    #region Override Method

    protected override void OnInit(IDictionary<string, object> query)
    {
        base.OnInit(query);
        LoginId = sessionServices.LoginId;
        IsLogin = appSettingServices.AppSettings.IsLogin;
        _medicalDictionary = appSettingServices.MedicalCheckSettingDictionary();
        ClientSelected = query.GetData<ClientInformationModel>();
        MedicalCheckProgresses = [];
        foreach(var status in ClientSelected.MedicalCheckProgressesDisplays)
        {
            var medicalCheckState = status.Clone();
            medicalCheckState.TestStatus.TextColor = _medicalDictionary[status.TestStatus.MedicalState].TextColor;
            medicalCheckState.TestStatus.ProgressColor = _medicalDictionary[status.TestStatus.MedicalState].BackgroundColor;
            MedicalCheckProgresses.Add(medicalCheckState);

        }
    }
    public override Task OnAppearingAsync()
    {
        IsMaskMode = sessionServices.IsMaskMode;
        NoteScroll = sessionServices.NoteViewScroll;
        PersonalNoteScroll = sessionServices.PersonalNoteViewScroll;
        return base.OnAppearingAsync();
    }

    public override Task OnDisappearingAsync()
    {
        sessionServices.NoteViewScroll = NoteScroll;
        sessionServices.PersonalNoteViewScroll = PersonalNoteScroll;
        return base.OnDisappearingAsync();
    }

    #endregion
    #region Private Method

    private void TransferStatusProcess(MedicalCheckStateModel dedicalCheckStateItem)
    {
        switch(dedicalCheckStateItem.TestStatus.MedicalState)
        {
            case MedicalState.Unchecked:
            case MedicalState.Next:
            case MedicalState.Pending:
                dedicalCheckStateItem.TestStatus = new TestStatusModel
                {
                    MedicalState = MedicalState.Stopped,
                    DisplayName = AppResources.Stopped,
                    StatusColor = AppColors.Grey40,
                    TextColor = _medicalDictionary[MedicalState.Stopped].TextColor,
                    ProgressColor = _medicalDictionary[MedicalState.Stopped].BackgroundColor
                };
                break;
            case MedicalState.Stopped:
                dedicalCheckStateItem.TestStatus =
                new TestStatusModel
                {
                    MedicalState = MedicalState.Unchecked,
                    DisplayName = AppResources.Unchecked,
                    StatusColor = AppColors.White,
                    TextColor = _medicalDictionary[MedicalState.Unchecked].TextColor,
                    ProgressColor = _medicalDictionary[MedicalState.Unchecked].BackgroundColor

                };
                break;
        }
    }
    #endregion
}