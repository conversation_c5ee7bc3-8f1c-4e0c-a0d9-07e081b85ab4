<?xml version="1.0" encoding="utf-8" ?>
<mvvm:BasePage
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.GuidanceSettingPage"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:mtk="clr-namespace:MemoryToolkit.Maui;assembly=MemoryToolkit.Maui"
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns:converter="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:vm="clr-namespace:ShinKenShinKun"
    x:DataType="app:GuidanceSettingViewModel"
    xmlns:app="clr-namespace:ShinKenShinKun">
    <Shell.BackButtonBehavior>
        <BackButtonBehavior
            IsEnabled="False"
            IsVisible="False" />
    </Shell.BackButtonBehavior>

    <Grid
        BackgroundColor="{x:Static ui:AppColors.HeaderColor}"
        RowDefinitions="80, *, Auto">
        <!--Header-->
        <Grid
            Grid.Row="0"
            ColumnDefinitions="*, Auto, *">
            <app:MaskButtonView
                Grid.Column="0"
                HorizontalOptions="Start"
                VerticalOptions="Center"
                Margin="{ui:EdgeInsets Left={x:Static ui:Dimens.Spacing122}}"
                ChangeModeCommand="{Binding ChangeModeCommand}"
                IsMaskMode="{Binding IsMaskMode}" />
            <Label
                Grid.Column="1"
                HorizontalOptions="Center"
                Text="{x:Static resources:AppResources.GuidanceSettingTitle}"
                Style="{Static ui:Styles.AppHeaderLabelStyle}" />
            <app:LoginUserView
                IsVisible="{Binding IsLogin}"
                LoginId="{Binding LoginId}"
                Grid.Column="2" />
        </Grid>
        
        <!--Content-->
        <Grid
            Grid.Row="1"
            ColumnDefinitions="3*,7*"
            BackgroundColor="{x:Static ui:AppColors.ContentBackgroud}">
            <app:PatientInfoView
                NoteScroll="{Binding NoteScroll}"
                PersonalNoteScroll="{Binding PersonalNoteScroll}"
                ShowNoteEditPopupCommand="{Binding ShowNoteEditPopupCommand}"
                IsMaskMode="{Binding IsMaskMode}"
                IsItemSelected="{Binding ClientSelected,
                    Converter={x:Static ui:AppConverters.ObjectToBoolConverter}}"
                ItemSelected="{Binding ClientSelected}"
                Grid.Column="0" />
            <telerik:RadBorder
                Style="{x:Static ui:Styles.FrameBorderStyle}"
                Margin="{ui:EdgeInsets All={x:Static ui:Dimens.Spacing10}}"
                Grid.Column="1">
                <!--Guide Items-->
                <app:GuidanceSelectionListView
                    GuideItems="{Binding GuideItems}"
                    SelectionChangedCommand="{Binding SelectButtonCommand}" />
            </telerik:RadBorder>
        </Grid>
        
        <!--Footer-->
        <Grid
            BackgroundColor="{x:Static ui:AppColors.LightCobaltBlue}"
            Padding="{x:Static ui:Dimens.SpacingSm2}"
            HeightRequest="{x:Static ui:Dimens.Height100}"
            Grid.Row="2"
            ColumnDefinitions="Auto">
            <app:BackButtonView
                BackCommand="{Binding BackCommand}"
                HorizontalOptions="Start"
                Grid.Column="0" />
        </Grid>
    </Grid>
</mvvm:BasePage>