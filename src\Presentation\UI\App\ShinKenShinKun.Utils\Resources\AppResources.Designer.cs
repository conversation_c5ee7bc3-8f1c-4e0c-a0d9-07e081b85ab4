﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace ShinKenShinKun.Utils.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class AppResources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal AppResources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("ShinKenShinKun.Utils.Resources.AppResources", typeof(AppResources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 追加.
        /// </summary>
        public static string Add {
            get {
                return ResourceManager.GetString("Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 年齢.
        /// </summary>
        public static string Age {
            get {
                return ResourceManager.GetString("Age", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}歳.
        /// </summary>
        public static string AgeStringFormat {
            get {
                return ResourceManager.GetString("AgeStringFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to すべて.
        /// </summary>
        public static string All {
            get {
                return ResourceManager.GetString("All", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 新けんしんくんの起動に失敗しました。設定ファイルを確認し、アプリを再起動してください。.
        /// </summary>
        public static string AppSettingErrorMessage {
            get {
                return ResourceManager.GetString("AppSettingErrorMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to エラー.
        /// </summary>
        public static string AppSettingErrorTitle {
            get {
                return ResourceManager.GetString("AppSettingErrorTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 平均値.
        /// </summary>
        public static string AverageValue {
            get {
                return ResourceManager.GetString("AverageValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Back.
        /// </summary>
        public static string Back {
            get {
                return ResourceManager.GetString("Back", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 一文字削除.
        /// </summary>
        public static string Backspace {
            get {
                return ResourceManager.GetString("Backspace", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 血圧.
        /// </summary>
        public static string BloodPressure {
            get {
                return ResourceManager.GetString("BloodPressure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 体脂肪.
        /// </summary>
        public static string BodyFat {
            get {
                return ResourceManager.GetString("BodyFat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BMI.
        /// </summary>
        public static string BodyMassIndex {
            get {
                return ResourceManager.GetString("BodyMassIndex", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to キャンセル.
        /// </summary>
        public static string Cancel {
            get {
                return ResourceManager.GetString("Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 一括解除.
        /// </summary>
        public static string CancelAll {
            get {
                return ResourceManager.GetString("CancelAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 保留解除.
        /// </summary>
        public static string CancelPending {
            get {
                return ResourceManager.GetString("CancelPending", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 状況確認モニタジュシステムの起動に失敗しました。もう一度「全体進捗」を押してください。.
        /// </summary>
        public static string CanNotRunExternalSystem {
            get {
                return ResourceManager.GetString("CanNotRunExternalSystem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 誘導変更.
        /// </summary>
        public static string ChangeGuide {
            get {
                return ResourceManager.GetString("ChangeGuide", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to クリア.
        /// </summary>
        public static string Clear {
            get {
                return ResourceManager.GetString("Clear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 削除.
        /// </summary>
        public static string ClearText {
            get {
                return ResourceManager.GetString("ClearText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 閉じる.
        /// </summary>
        public static string Close {
            get {
                return ResourceManager.GetString("Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 済.
        /// </summary>
        public static string Completed {
            get {
                return ResourceManager.GetString("Completed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 確定.
        /// </summary>
        public static string Confirm {
            get {
                return ResourceManager.GetString("Confirm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}に誘導しますか？.
        /// </summary>
        public static string ConfirmNavigationMessage {
            get {
                return ResourceManager.GetString("ConfirmNavigationMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 新けんしんくんサ ー バとの接続時にエラ ー が発生しました。ネットワ ー ク環境を確認してくたさい。.
        /// </summary>
        public static string ConnectionErrors {
            get {
                return ResourceManager.GetString("ConnectionErrors", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 生年月日.
        /// </summary>
        public static string DateOfBirth {
            get {
                return ResourceManager.GetString("DateOfBirth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0:yyyy/MM/dd}.
        /// </summary>
        public static string DatetimeStringFormat {
            get {
                return ResourceManager.GetString("DatetimeStringFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 済.
        /// </summary>
        public static string Done {
            get {
                return ResourceManager.GetString("Done", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EDIT.
        /// </summary>
        public static string EDIT {
            get {
                return ResourceManager.GetString("EDIT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 全て.
        /// </summary>
        public static string Entire {
            get {
                return ResourceManager.GetString("Entire", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to エラー.
        /// </summary>
        public static string Error {
            get {
                return ResourceManager.GetString("Error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 検査画面.
        /// </summary>
        public static string ExaminationScreen {
            get {
                return ResourceManager.GetString("ExaminationScreen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 外.
        /// </summary>
        public static string Excluded {
            get {
                return ResourceManager.GetString("Excluded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 女.
        /// </summary>
        public static string Female {
            get {
                return ResourceManager.GetString("Female", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 第一候補.
        /// </summary>
        public static string FirstCandidateTitle {
            get {
                return ResourceManager.GetString("FirstCandidateTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 性別.
        /// </summary>
        public static string Gender {
            get {
                return ResourceManager.GetString("Gender", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 誘導.
        /// </summary>
        public static string Guidance {
            get {
                return ResourceManager.GetString("Guidance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 誘導調整.
        /// </summary>
        public static string GuidanceAdjustment {
            get {
                return ResourceManager.GetString("GuidanceAdjustment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 誘導先.
        /// </summary>
        public static string GuidanceDestination {
            get {
                return ResourceManager.GetString("GuidanceDestination", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 誘導変更.
        /// </summary>
        public static string GuidanceSettingTitle {
            get {
                return ResourceManager.GetString("GuidanceSettingTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 誘導開始.
        /// </summary>
        public static string GuideOpen {
            get {
                return ResourceManager.GetString("GuideOpen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to の誘導を開始しますか？.
        /// </summary>
        public static string GuideOpenDialogTitle {
            get {
                return ResourceManager.GetString("GuideOpenDialogTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 誘導中断.
        /// </summary>
        public static string GuideStop {
            get {
                return ResourceManager.GetString("GuideStop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to の誘導を中断しますか？.
        /// </summary>
        public static string GuideStopDialogTitle {
            get {
                return ResourceManager.GetString("GuideStopDialogTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 聴力選別.
        /// </summary>
        public static string HearingSelection {
            get {
                return ResourceManager.GetString("HearingSelection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 聴力閾値.
        /// </summary>
        public static string HearingThreshold {
            get {
                return ResourceManager.GetString("HearingThreshold", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 身長.
        /// </summary>
        public static string Height {
            get {
                return ResourceManager.GetString("Height", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 最高血圧1.
        /// </summary>
        public static string HighestBloodPressure1 {
            get {
                return ResourceManager.GetString("HighestBloodPressure1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 最高血圧2.
        /// </summary>
        public static string HightestBloodPressure2 {
            get {
                return ResourceManager.GetString("HightestBloodPressure2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Home.
        /// </summary>
        public static string Home {
            get {
                return ResourceManager.GetString("Home", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HOME.
        /// </summary>
        public static string HomeButtonLabel {
            get {
                return ResourceManager.GetString("HomeButtonLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to けんしんくんHOME.
        /// </summary>
        public static string HomeTitle {
            get {
                return ResourceManager.GetString("HomeTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ID.
        /// </summary>
        public static string Id {
            get {
                return ResourceManager.GetString("Id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 個別進捗.
        /// </summary>
        public static string IndividualProgress {
            get {
                return ResourceManager.GetString("IndividualProgress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 検査中.
        /// </summary>
        public static string InExamination {
            get {
                return ResourceManager.GetString("InExamination", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ID入力.
        /// </summary>
        public static string InputClientIdButton {
            get {
                return ResourceManager.GetString("InputClientIdButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 検査項目選択.
        /// </summary>
        public static string InspectionItemSelection {
            get {
                return ResourceManager.GetString("InspectionItemSelection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to カナ指名.
        /// </summary>
        public static string KanaName {
            get {
                return ResourceManager.GetString("KanaName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1000Hz左.
        /// </summary>
        public static string Left1000Hz {
            get {
                return ResourceManager.GetString("Left1000Hz", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 2000Hz左.
        /// </summary>
        public static string Left2000Hz {
            get {
                return ResourceManager.GetString("Left2000Hz", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 250Hz左.
        /// </summary>
        public static string Left250Hz {
            get {
                return ResourceManager.GetString("Left250Hz", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 4000Hz左.
        /// </summary>
        public static string Left4000Hz {
            get {
                return ResourceManager.GetString("Left4000Hz", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 500Hz左.
        /// </summary>
        public static string Left500Hz {
            get {
                return ResourceManager.GetString("Left500Hz", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 8000Hz左.
        /// </summary>
        public static string Left8000Hz {
            get {
                return ResourceManager.GetString("Left8000Hz", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 左矯正.
        /// </summary>
        public static string LeftEye {
            get {
                return ResourceManager.GetString("LeftEye", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 左裸眼.
        /// </summary>
        public static string LeftNakedEye {
            get {
                return ResourceManager.GetString("LeftNakedEye", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to [{0}] User: {1} | IP: {2} | Status: {3}.
        /// </summary>
        public static string LogFormat {
            get {
                return ResourceManager.GetString("LogFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ログイン.
        /// </summary>
        public static string Login {
            get {
                return ResourceManager.GetString("Login", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ログインIDを入力してください。.
        /// </summary>
        public static string LoginIdEmptyInvalid {
            get {
                return ResourceManager.GetString("LoginIdEmptyInvalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ログインユーザ.
        /// </summary>
        public static string LoginIdLabel {
            get {
                return ResourceManager.GetString("LoginIdLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ログインIDまたはパスワードが不正です。.
        /// </summary>
        public static string LoginIdOrPasswordInvalid {
            get {
                return ResourceManager.GetString("LoginIdOrPasswordInvalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ログイン.
        /// </summary>
        public static string LoginScreenTitle {
            get {
                return ResourceManager.GetString("LoginScreenTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ログアウト.
        /// </summary>
        public static string Logout {
            get {
                return ResourceManager.GetString("Logout", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ログアウトしますか？.
        /// </summary>
        public static string LogoutMessage {
            get {
                return ResourceManager.GetString("LogoutMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 最低血圧1.
        /// </summary>
        public static string LowestBloodPressure1 {
            get {
                return ResourceManager.GetString("LowestBloodPressure1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 最低血圧2.
        /// </summary>
        public static string LowestBloodPressure2 {
            get {
                return ResourceManager.GetString("LowestBloodPressure2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 男.
        /// </summary>
        public static string Male {
            get {
                return ResourceManager.GetString("Male", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to マスク.
        /// </summary>
        public static string Mask {
            get {
                return ResourceManager.GetString("Mask", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OFF.
        /// </summary>
        public static string MaskOff {
            get {
                return ResourceManager.GetString("MaskOff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ON.
        /// </summary>
        public static string MaskOn {
            get {
                return ResourceManager.GetString("MaskOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ＊＊＊＊＊＊＊.
        /// </summary>
        public static string MaskText {
            get {
                return ResourceManager.GetString("MaskText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 計測.
        /// </summary>
        public static string Measurement {
            get {
                return ResourceManager.GetString("Measurement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 検査種別.
        /// </summary>
        public static string MedicalChecklist {
            get {
                return ResourceManager.GetString("MedicalChecklist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 表示検査.
        /// </summary>
        public static string NameMedicalCategory {
            get {
                return ResourceManager.GetString("NameMedicalCategory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 次.
        /// </summary>
        public static string Next {
            get {
                return ResourceManager.GetString("Next", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to いいえ.
        /// </summary>
        public static string No {
            get {
                return ResourceManager.GetString("No", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to データなし.
        /// </summary>
        public static string NoData {
            get {
                return ResourceManager.GetString("NoData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 不.
        /// </summary>
        public static string NoDefine {
            get {
                return ResourceManager.GetString("NoDefine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 備考.
        /// </summary>
        public static string Notes {
            get {
                return ResourceManager.GetString("Notes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 未.
        /// </summary>
        public static string NotYet {
            get {
                return ResourceManager.GetString("NotYet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}回目.
        /// </summary>
        public static string NumberOf {
            get {
                return ResourceManager.GetString("NumberOf", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OFF.
        /// </summary>
        public static string Off {
            get {
                return ResourceManager.GetString("Off", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Offline.
        /// </summary>
        public static string Offline {
            get {
                return ResourceManager.GetString("Offline", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OK.
        /// </summary>
        public static string Ok {
            get {
                return ResourceManager.GetString("Ok", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 過去値.
        /// </summary>
        public static string OldValue {
            get {
                return ResourceManager.GetString("OldValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ON.
        /// </summary>
        public static string On {
            get {
                return ResourceManager.GetString("On", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Online.
        /// </summary>
        public static string Online {
            get {
                return ResourceManager.GetString("Online", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 備考 {0}.
        /// </summary>
        public static string OrderNote {
            get {
                return ResourceManager.GetString("OrderNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パスワード.
        /// </summary>
        public static string Password {
            get {
                return ResourceManager.GetString("Password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パスワードを入力してください。.
        /// </summary>
        public static string PasswordEmptyInvalid {
            get {
                return ResourceManager.GetString("PasswordEmptyInvalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 受診者情報.
        /// </summary>
        public static string PatientInformation {
            get {
                return ResourceManager.GetString("PatientInformation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 受診者一括保留.
        /// </summary>
        public static string PatientPendingScreen {
            get {
                return ResourceManager.GetString("PatientPendingScreen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 保.
        /// </summary>
        public static string Pending {
            get {
                return ResourceManager.GetString("Pending", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 一括保留.
        /// </summary>
        public static string PendingAll {
            get {
                return ResourceManager.GetString("PendingAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 保留.
        /// </summary>
        public static string PendingButton {
            get {
                return ResourceManager.GetString("PendingButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}は保留中です。　　
        ///{0}に誘導しますか？.
        /// </summary>
        public static string PendingNavigationMessage {
            get {
                return ResourceManager.GetString("PendingNavigationMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 人.
        /// </summary>
        public static string Person {
            get {
                return ResourceManager.GetString("Person", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 個人特記.
        /// </summary>
        public static string PersonalNotes {
            get {
                return ResourceManager.GetString("PersonalNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 予定.
        /// </summary>
        public static string Plan {
            get {
                return ResourceManager.GetString("Plan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 進捗.
        /// </summary>
        public static string Progress {
            get {
                return ResourceManager.GetString("Progress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 脈拍1.
        /// </summary>
        public static string Pulse1 {
            get {
                return ResourceManager.GetString("Pulse1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 脈拍2.
        /// </summary>
        public static string Pulse2 {
            get {
                return ResourceManager.GetString("Pulse2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to アプリケーションを終了しますか？.
        /// </summary>
        public static string QuitAppTitle {
            get {
                return ResourceManager.GetString("QuitAppTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 受付番号.
        /// </summary>
        public static string ReceiptNumber {
            get {
                return ResourceManager.GetString("ReceiptNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}は必須検査が終了していません。
        ///{0}に誘導しますか？.
        /// </summary>
        public static string RequiredTestNotCompletedMessage {
            get {
                return ResourceManager.GetString("RequiredTestNotCompletedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 確保を解除しますがよろしいですか？.
        /// </summary>
        public static string ReserveCancelTitle {
            get {
                return ResourceManager.GetString("ReserveCancelTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 確保.
        /// </summary>
        public static string Reserved {
            get {
                return ResourceManager.GetString("Reserved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 他の端末で確保されているため、確保できません。.
        /// </summary>
        public static string ReserveNoCanceltitle {
            get {
                return ResourceManager.GetString("ReserveNoCanceltitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 他の受診者を確保済みです。
        ///他の受診者の確保を解除してから確保を行ってください。.
        /// </summary>
        public static string ReserveNoConfirmTitle {
            get {
                return ResourceManager.GetString("ReserveNoConfirmTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1000Hz右.
        /// </summary>
        public static string Right1000Hz {
            get {
                return ResourceManager.GetString("Right1000Hz", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 2000Hz右.
        /// </summary>
        public static string Right2000Hz {
            get {
                return ResourceManager.GetString("Right2000Hz", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 250Hz右.
        /// </summary>
        public static string Right250Hz {
            get {
                return ResourceManager.GetString("Right250Hz", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 4000Hz右.
        /// </summary>
        public static string Right4000Hz {
            get {
                return ResourceManager.GetString("Right4000Hz", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 500Hz右.
        /// </summary>
        public static string Right500Hz {
            get {
                return ResourceManager.GetString("Right500Hz", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 8000Hz右.
        /// </summary>
        public static string Right8000Hz {
            get {
                return ResourceManager.GetString("Right8000Hz", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 右矯正.
        /// </summary>
        public static string RightEye {
            get {
                return ResourceManager.GetString("RightEye", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 右裸眼.
        /// </summary>
        public static string RightNakedEye {
            get {
                return ResourceManager.GetString("RightNakedEye", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save.
        /// </summary>
        public static string Save {
            get {
                return ResourceManager.GetString("Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 検索.
        /// </summary>
        public static string Search {
            get {
                return ResourceManager.GetString("Search", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to すべて選択.
        /// </summary>
        public static string SelectAll {
            get {
                return ResourceManager.GetString("SelectAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 保留検査選択.
        /// </summary>
        public static string SelectionPendingTitle {
            get {
                return ResourceManager.GetString("SelectionPendingTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 中止検査選択.
        /// </summary>
        public static string SelectionStopTitle {
            get {
                return ResourceManager.GetString("SelectionStopTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 中止理由選択.
        /// </summary>
        public static string SelectReasonForCancellation {
            get {
                return ResourceManager.GetString("SelectReasonForCancellation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 検査種別選択.
        /// </summary>
        public static string SelectTheTestType {
            get {
                return ResourceManager.GetString("SelectTheTestType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 終了.
        /// </summary>
        public static string Shutdown {
            get {
                return ResourceManager.GetString("Shutdown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to コンピュータをシャットダウンしますか？.
        /// </summary>
        public static string ShutdownDeviceTitle {
            get {
                return ResourceManager.GetString("ShutdownDeviceTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 視力.
        /// </summary>
        public static string Sight {
            get {
                return ResourceManager.GetString("Sight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 状態.
        /// </summary>
        public static string Status {
            get {
                return ResourceManager.GetString("Status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 一括中止.
        /// </summary>
        public static string StopAll {
            get {
                return ResourceManager.GetString("StopAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 中止.
        /// </summary>
        public static string StopButton {
            get {
                return ResourceManager.GetString("StopButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 中止検査選択.
        /// </summary>
        public static string StopExamineScreen {
            get {
                return ResourceManager.GetString("StopExamineScreen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 止.
        /// </summary>
        public static string Stopped {
            get {
                return ResourceManager.GetString("Stopped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to けんしんくん.
        /// </summary>
        public static string SystemName {
            get {
                return ResourceManager.GetString("SystemName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 検査結果.
        /// </summary>
        public static string TestResult {
            get {
                return ResourceManager.GetString("TestResult", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 検査画面選択.
        /// </summary>
        public static string TestSelectionPageHeader {
            get {
                return ResourceManager.GetString("TestSelectionPageHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 検査状況.
        /// </summary>
        public static string TestStatus {
            get {
                return ResourceManager.GetString("TestStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 戻る.
        /// </summary>
        public static string TurnBack {
            get {
                return ResourceManager.GetString("TurnBack", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 未.
        /// </summary>
        public static string Unchecked {
            get {
                return ResourceManager.GetString("Unchecked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 更新.
        /// </summary>
        public static string Update {
            get {
                return ResourceManager.GetString("Update", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ログインID.
        /// </summary>
        public static string UserId {
            get {
                return ResourceManager.GetString("UserId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 待ち時間.
        /// </summary>
        public static string WaitingTime {
            get {
                return ResourceManager.GetString("WaitingTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 体重.
        /// </summary>
        public static string Weight {
            get {
                return ResourceManager.GetString("Weight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to はい.
        /// </summary>
        public static string Yes {
            get {
                return ResourceManager.GetString("Yes", resourceCulture);
            }
        }
    }
}
