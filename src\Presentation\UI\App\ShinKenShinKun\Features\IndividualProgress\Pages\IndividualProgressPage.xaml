<?xml version="1.0" encoding="utf-8" ?>
<mvvm:BasePage
    x:Class="ShinKenShinKun.IndividualProgressPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns:app="clr-namespace:ShinKenShinKun"
    x:DataType="app:IndividualProgressViewModel">
    <Shell.BackButtonBehavior>
        <BackButtonBehavior
            IsVisible="False"
            IsEnabled="False" />
    </Shell.BackButtonBehavior>
    <Grid
        BackgroundColor="{x:Static ui:AppColors.LightCobaltBlue}"
        RowDefinitions="80, *, Auto">
        <Grid
            Grid.Row="0"
            ColumnDefinitions="*, Auto, *">
            <HorizontalStackLayout
                Grid.Column="0"
                VerticalOptions="Center"
                Spacing="{x:Static ui:Dimens.SpacingXl}"
                Margin="{ui:EdgeInsets Left={x:Static ui:Dimens.SpacingSm2}}">
                <app:HomeButtonView
                    HorizontalOptions="Start"
                    GoHomeCommand="{Binding GoHomeCommand}" />
                <app:MaskButtonView
                    HorizontalOptions="Start"
                    VerticalOptions="Center"
                    ChangeModeCommand="{Binding ChangeModeCommand}"
                    IsMaskMode="{Binding IsMaskMode}" />
            </HorizontalStackLayout>

            <HorizontalStackLayout
                Grid.Column="1"
                HorizontalOptions="Center"
                Spacing="{x:Static ui:Dimens.SpacingLg}">
                <Label
                    Text="{x:Static resources:AppResources.IndividualProgress}"
                    Style="{Static ui:Styles.AppHeaderLabelStyle}" />
                <Label
                    Text="{Binding MedicalMachineName}"
                    Style="{Static ui:Styles.AppHeaderLabelStyle}" />
            </HorizontalStackLayout>

            <Grid
                IsVisible="{Binding IsLogin}"
                HorizontalOptions="End"
                VerticalOptions="Center"
                Margin="{ui:EdgeInsets 
                 Right={x:Static ui:Dimens.SpacingS}}"
                RowSpacing="{x:Static ui:Dimens.SpacingXs}"
                RowDefinitions="auto, 1"
                ColumnDefinitions="*, *, *, *"
                Grid.Column="2">
                <HorizontalStackLayout
                    Margin="{ui:EdgeInsets 
                     Right={x:Static ui:Dimens.Spacing20}}"
                    Grid.RowSpan="1"
                    Grid.ColumnSpan="4">
                    <Label
                        Style="{x:Static ui:Styles.NotoSansThinStyle}"
                        Text="{Binding TestStaff}"
                        FontSize="{x:Static ui:Dimens.FontSizeT7}"
                        Padding="{ui:EdgeInsets 
                         Right={x:Static ui:Dimens.SpacingXl}}"
                        VerticalTextAlignment="Center" />
                    <Label
                        Style="{x:Static ui:Styles.NotoSansThinStyle}"
                        Text="{Binding CurrentUser}"
                        FontSize="{x:Static ui:Dimens.FontSizeT5}"
                        VerticalTextAlignment="Center" />
                </HorizontalStackLayout>
                <BoxView
                    Grid.Row="1"
                    Grid.Column="1"
                    Grid.ColumnSpan="3"
                    BackgroundColor="{x:Static ui:AppColors.LightSlateBlue}" />
            </Grid>
        </Grid>

        <Grid
            Grid.Row="1"
            ColumnDefinitions="*,*"
            BackgroundColor="{x:Static ui:AppColors.ContentBackgroud}">
            <app:IndividualPatientsInfo
                Grid.Column="0" />
            <app:IndividualPatientDetail 
                Grid.Column="1"/>
        </Grid>
        
        <Grid
            BackgroundColor="{x:Static ui:AppColors.LightCobaltBlue}"
            Padding="{x:Static ui:Dimens.SpacingSm2}"
            HeightRequest="{x:Static ui:Dimens.Height100}"
            Grid.Row="2"
            ColumnDefinitions="Auto,*,Auto">
            <app:BackButtonView
                BackCommand="{Binding BackCommand}"
                HorizontalOptions="Start"
                Grid.Column="0" />
        </Grid>
    </Grid>
</mvvm:BasePage>