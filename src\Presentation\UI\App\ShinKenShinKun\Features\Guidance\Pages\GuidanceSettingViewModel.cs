﻿namespace ShinKenShinKun;

public partial class GuidanceSettingViewModel(
    IAppNavigator appNavigator,
    IFileSystemService fileSystemService,
    IAppSettingServices appSettingServices,
    ISessionServices sessionServices,
    IMapper mapper)
    : NavigationAwareBaseViewModel(appNavigator)
{
    #region ObservableProperties
    [ObservableProperty] private ClientInformationModel? clientSelected;
    [ObservableProperty] private bool isMaskMode;
    [ObservableProperty] private string loginId;
    [ObservableProperty] private ObservableCollection<GuideItem> guideItems;
    [ObservableProperty] private bool isLogin;
    [ObservableProperty] private ScrollPositionModel noteScroll;
    [ObservableProperty] private ScrollPositionModel personalNoteScroll;
    #endregion ObservableProperties

    #region RelayCommand

    [RelayCommand]
    private void ChangeMode()
    {
        IsMaskMode = !IsMaskMode;
        sessionServices.IsMaskMode = IsMaskMode;
    }

    [RelayCommand]
    private async Task SelectButton(GuideItem guideItem)
    {
        switch(guideItem.Status)
        {
            case 1:
                await GeneralProcess(Format(AppResources.ConfirmNavigationMessage, guideItem.MedicalCheckName), guideItem);
                break;
            case 2:
                break;
            case 3:
                await GeneralProcess(Format(AppResources.PendingNavigationMessage, guideItem.MedicalCheckName), guideItem);
                break;
            case 4:
                await GeneralProcess(Format(AppResources.RequiredTestNotCompletedMessage, guideItem.MedicalCheckName), guideItem);
                break;
            case 5:
                break;
            default:
                break;
        }
    }

    [RelayCommand]
    private async Task ShowNoteEditPopup()
    {
        if(ClientSelected != null)
        {
            var popup = new NoteEditPopup();
            popup.Note = ClientSelected.Note;
            await AppNavigator.ShowPopupAsync(popup);
        }
    }

    #endregion

    #region Override Method

    protected override void OnInit(IDictionary<string, object> query)
    {
        base.OnInit(query);
        LoginId = sessionServices.LoginId;
        IsLogin = appSettingServices.AppSettings.IsLogin;
        ClientSelected = query.GetData<ClientInformationModel>();
    }

    public override Task OnAppearingAsync()
    {
        GuideItems = mapper.Map<ObservableCollection<GuideItem>>(fileSystemService.ReadGuidanceExcelFile());
        IsMaskMode = sessionServices.IsMaskMode;
        NoteScroll = sessionServices.NoteViewScroll;
        PersonalNoteScroll = sessionServices.PersonalNoteViewScroll;
        return Task.CompletedTask;
    }

    public override Task OnDisappearingAsync()
    {
        sessionServices.NoteViewScroll = NoteScroll;
        sessionServices.PersonalNoteViewScroll = PersonalNoteScroll;
        return base.OnDisappearingAsync();
    }

    #endregion

    #region Private Methods
    private async Task GeneralProcess(string message, GuideItem guideItem)
    {
        if(await ShowDialog(message))
        {
            await AppNavigator.GoBackAsync(false, ClientSelected);
        }
    }

    private async Task<bool> ShowDialog(string message)
    {
        return await AppNavigator.ShowConfirmationDialog(message);
    }


    #endregion
}
