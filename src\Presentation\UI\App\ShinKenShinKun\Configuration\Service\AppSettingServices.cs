﻿
namespace ShinKenShinKun;

public class AppSettingServices(IConfiguration configuration, IDialogMessageService dialogMessageService) : IAppSettingServices
{
    private bool isError = false;
    private AppSettings appSettings;
    public AppSettings AppSettings => appSettings ??= GetAppSettings();

    private readonly Lazy<List<DialogMessageDto>>? dialogMessages = new(dialogMessageService.GetAllMessage());

    public List<DialogMessageDto> DialogMessages => dialogMessages.Value;

    public Dictionary<MedicalState, MedicalCheckProgressSetting> MedicalCheckSettingDictionary()
    {
        var dictionary = new Dictionary<MedicalState, MedicalCheckProgressSetting>();
        foreach (var statusJson in AppSettings.MedicalCheckProgressSetting)
        {
            dictionary.TryAdd(statusJson.Status, statusJson);
        }
        return dictionary;
    }

    public IList<AreaModel> GetAreaSettings()
    {
        return AppSettings.AreaSettings ?? new List<AreaModel>();
    }

    public SelectCheckSettings GetSelectCheckSettings()
    {
        return AppSettings.SelectCheckSettings ?? new SelectCheckSettings();
    }

    private AppSettings GetAppSettings()
    {
        isError = false;
        try
        {
            var settings = configuration
               .Get<AppSettings>();
            return settings ?? new AppSettings();
        }
        catch (Exception ex)
        {
            Log.Error(ex, AppResources.Error);
        }
        isError = true;
        return new AppSettings();
    }

    public bool IsErrorReadingAppSettings()
    {
        var appSettings = GetAppSettings();
        return isError || !configuration.GetChildren().Any();
    }
}