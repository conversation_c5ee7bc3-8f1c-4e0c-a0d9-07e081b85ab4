namespace ShinKenShinKun;

public partial class LoginUserView : ContentView
{

    public static new readonly BindableProperty IsVisibleProperty = BindableProperty.Create(
        nameof(IsVisible),
        typeof(bool),
        typeof(LoginUserView),
        false,
        BindingMode.TwoWay);

    public static readonly BindableProperty LoginIdProperty = BindableProperty.Create(
        nameof(LoginId),
        typeof(string),
        typeof(LoginUserView),
        string.Empty,
        BindingMode.TwoWay);

    public LoginUserView()
    {
        InitializeComponent();
    }

    public new bool IsVisible
    {
        get => (bool)GetValue(IsVisibleProperty);
        set => SetValue(IsVisibleProperty, value);
    }

    public string LoginId
    {
        get => (string)GetValue(LoginIdProperty);
        set => SetValue(LoginIdProperty, value);
    }
}