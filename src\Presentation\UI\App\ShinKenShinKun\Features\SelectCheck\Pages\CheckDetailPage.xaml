<?xml version="1.0" encoding="utf-8" ?>
<mvvm:BasePage
    x:Class="ShinKenShinKun.CheckDetailPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:ShinKenShinKun"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:resource="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    Title="CheckDetailPage"
    x:DataType="app:CheckDetailPageViewModel">
    <Shell.BackButtonBehavior>
        <BackButtonBehavior
            IsEnabled="False"
            IsVisible="False" />
    </Shell.BackButtonBehavior>
    <Grid
        RowDefinitions="80,*,auto"
        BackgroundColor="{Static ui:AppColors.Platinum}">
        <!--Header-->
        <Grid
            Grid.Row="0"
            BackgroundColor="{Static ui:AppColors.LightCobaltBlue}"
            ColumnDefinitions="*, Auto, *">
            <Grid
                ColumnDefinitions="auto"
                Grid.Column="0"
                Margin="{ui:EdgeInsets Left={x:Static ui:Dimens.SpacingSm2}}"
                VerticalOptions="Center">
                <Entry
                    x:Name="HiddenEntry"
                    Opacity="0"
                    WidthRequest="0"
                    HeightRequest="0" />
                <app:HomeButtonView
                    GoHomeCommand="{Binding GoHomeCommand}" />
            </Grid>
            <Label
                Grid.Column="1"
                Text="{Binding CheckName}"
                Style="{Static ui:Styles.AppHeaderLabelStyle}" />
        </Grid>

        <!--Content-->
        <ScrollView
            Grid.Row="1"
            Margin="{x:Static ui:Dimens.Spacing20}">
            <StackLayout x:Name="DynamicContentContainer"
                         BindableLayout.ItemsSource="{Binding AreaSettings}"
                         Spacing="10">
                <BindableLayout.ItemTemplate>
                    <DataTemplate>
                        <Border BackgroundColor="{x:Static ui:AppColors.White}"
                                Stroke="{x:Static ui:AppColors.LightGray}"
                                StrokeThickness="1"
                                Padding="10"
                                Margin="5">
                            <Grid x:Name="AreaGrid">
                                <!-- Dynamic grid content will be created in code-behind -->
                            </Grid>
                        </Border>
                    </DataTemplate>
                </BindableLayout.ItemTemplate>
            </StackLayout>
        </ScrollView>

        <!--Footer-->
        <Grid
            Grid.Row="2"
            Padding="{ui:EdgeInsets Vertical={Static ui:Dimens.Spacing5},
            Horizontal={Static ui:Dimens.Spacing3}}"
            BackgroundColor="{Static ui:AppColors.LightCobaltBlue}">
            <telerik:RadBorder
                BackgroundColor="{x:Static ui:AppColors.GrayBack}"
                CornerRadius="{x:Static ui:Dimens.RadCornerRadius10}"
                Padding="{x:Static ui:Dimens.SpacingSm2}"
                WidthRequest="{x:Static ui:Dimens.Spacing160}"
                HorizontalOptions="Start"
                Grid.Column="0">
                <telerik:RadBorder.GestureRecognizers>
                    <TapGestureRecognizer
                        Command="{Binding GoBackCommand}" />
                </telerik:RadBorder.GestureRecognizers>
                <StackLayout
                    Orientation="Vertical"
                    HorizontalOptions="Center"
                    VerticalOptions="Center">
                    <Image
                        Source="{x:Static ui:Icons.BackIcon}"
                        HeightRequest="{x:Static ui:Dimens.Height32}"
                        WidthRequest="{x:Static ui:Dimens.Width32}" />
                    <Label
                        Text="{x:Static resource:AppResources.Back}"
                        TextTransform="Uppercase"
                        TextColor="{x:Static ui:AppColors.White}"
                        FontSize="{x:Static ui:Dimens.FontSizeT4}"
                        FontFamily="{x:Static ui:FontNames.NotoSansJPRegular}"
                        HorizontalOptions="Center" />
                </StackLayout>
            </telerik:RadBorder>
        </Grid>
    </Grid>
</mvvm:BasePage>
