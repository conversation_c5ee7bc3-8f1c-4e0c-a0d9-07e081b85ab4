<?xml version="1.0" encoding="utf-8" ?>
<mvvm:BasePage x:Class="ShinKenShinKun.CheckDetailPage"
               xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
               xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
               xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
               xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
               xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
               x:DataType="local:CheckDetailPageViewModel"
               xmlns:local="clr-namespace:ShinKenShinKun"
               Title="{Binding CheckName}">

    <Grid RowDefinitions="Auto,*,Auto">
        <!-- Header -->
        <Grid Grid.Row="0"
              BackgroundColor="{x:Static ui:AppColors.White}"
              Padding="{ui:EdgeInsets All={x:Static ui:Dimens.SpacingMd}}">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <telerik:RadButton Grid.Column="0"
                               Command="{Binding GoBackCommand}"
                               BackgroundColor="Transparent"
                               BorderThickness="0"
                               WidthRequest="50"
                               HeightRequest="50"
                               Margin="10,10,0,10">
                <telerik:RadButton.Content>
                    <telerik:RadPath Data="{x:Static ui:PathGeometry.ArrowLeft}"
                                     Fill="{x:Static ui:AppColors.HanBlue}"
                                     WidthRequest="24"
                                     HeightRequest="24"/>
                </telerik:RadButton.Content>
            </telerik:RadButton>

            <Label Grid.Column="1"
                   Text="{Binding CheckName}"
                   FontSize="{x:Static ui:Dimens.FontSizeT3}"
                   FontAttributes="Bold"
                   TextColor="{x:Static ui:AppColors.HanBlue}"
                   VerticalOptions="Center"
                   HorizontalOptions="Start"
                   Margin="10,0,10,0"/>
        </Grid>

        <!-- Body - Dynamic Content -->
        <ScrollView Grid.Row="1"
                    BackgroundColor="{x:Static ui:AppColors.White}"
                    Padding="{ui:EdgeInsets All={x:Static ui:Dimens.SpacingMd}}">
            <StackLayout x:Name="DynamicContentContainer"
                         BindableLayout.ItemsSource="{Binding AreaSettings}"
                         Spacing="10">
                <BindableLayout.ItemTemplate>
                    <DataTemplate>
                        <Border BackgroundColor="{x:Static ui:AppColors.White}"
                                Stroke="{x:Static ui:AppColors.LightGray}"
                                StrokeThickness="1"
                                Padding="10"
                                Margin="5">
                            <Grid x:Name="AreaGrid">
                                <!-- Dynamic grid content will be created in code-behind -->
                            </Grid>
                        </Border>
                    </DataTemplate>
                </BindableLayout.ItemTemplate>
            </StackLayout>
        </ScrollView>

        <!-- Footer -->
        <Grid Grid.Row="2" 
              BackgroundColor="{x:Static ui:AppColors.White}"
              Padding="20,10,20,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <telerik:RadButton Grid.Column="0"
                               Text="キャンセル"
                               Command="{Binding CancelCommand}"
                               BackgroundColor="{x:Static ui:AppColors.LightGray}"
                               TextColor="{x:Static ui:AppColors.DarkGray}"
                               FontSize="{x:Static ui:Dimens.FontSizeT4}"
                               HeightRequest="50"
                               Margin="0,0,10,0"/>

            <telerik:RadButton Grid.Column="1"
                               Text="保存"
                               Command="{Binding SaveCommand}"
                               BackgroundColor="{x:Static ui:AppColors.HanBlue}"
                               TextColor="{x:Static ui:AppColors.White}"
                               FontSize="{x:Static ui:Dimens.FontSizeT4}"
                               HeightRequest="50"
                               Margin="10,0,0,0"/>
        </Grid>
    </Grid>
</mvvm:BasePage>
