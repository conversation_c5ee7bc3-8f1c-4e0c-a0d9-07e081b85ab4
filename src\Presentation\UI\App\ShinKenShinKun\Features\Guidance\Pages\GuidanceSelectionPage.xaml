<?xml version="1.0" encoding="utf-8" ?>
<mvvm:BasePage
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.GuidanceSelectionPage"
    xmlns:app="clr-namespace:ShinKenShinKun"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    x:DataType="app:GuidanceSelectionViewModel"
    x:Name="this">
    <Shell.BackButtonBehavior>
        <BackButtonBehavior
            IsEnabled="False"
            IsVisible="False" />
    </Shell.BackButtonBehavior>

    <Grid
        BackgroundColor="{x:Static ui:AppColors.HeaderColor}"
        RowDefinitions="80, *, Auto">
        <!--Header-->
        <Grid
            Grid.Row="0"
            ColumnDefinitions="*, Auto, *">
            <app:MaskButtonView
                Grid.Column="0"
                HorizontalOptions="Start"
                VerticalOptions="Center"
                Margin="{ui:EdgeInsets Left={x:Static ui:Dimens.Spacing122}}"
                ChangeModeCommand="{Binding ChangeModeCommand}"
                IsMaskMode="{Binding IsMaskMode}" />
            <HorizontalStackLayout
                Grid.Column="1"
                HorizontalOptions="Center"
                Spacing="{x:Static ui:Dimens.SpacingLg}">
                <Label
                    Text="{x:Static resources:AppResources.ExaminationScreen}"
                    Style="{Static ui:Styles.AppHeaderLabelStyle}" />
                <Label
                    Text="{Binding MedicalMachineName}"
                    Style="{Static ui:Styles.AppHeaderLabelStyle}" />
            </HorizontalStackLayout>

            <app:LoginUserView
                IsVisible="{Binding IsLogin}"
                LoginId="{Binding LoginId}"
                Grid.Column="2" />
        </Grid>

        <!--Content-->
        <Grid
            Grid.Row="1"
            ColumnDefinitions="3*,7*"
            BackgroundColor="{x:Static ui:AppColors.ContentBackgroud}">
            <app:PatientInfoView
                NoteScroll="{Binding NoteScroll}"
                PersonalNoteScroll="{Binding PersonalNoteScroll}"
                ShowNoteEditPopupCommand="{Binding ShowNoteEditPopupCommand}"
                IsItemSelected="{Binding ClientSelected, 
                    Converter={x:Static ui:AppConverters.ObjectToBoolConverter}}"
                IsMaskMode="{Binding IsMaskMode}"
                ItemSelected="{Binding ClientSelected}" />
            <telerik:RadBorder
                Style="{x:Static ui:Styles.FrameBorderStyle}"
                Margin="{ui:EdgeInsets All={x:Static ui:Dimens.Spacing10}}"
                Grid.Column="1">
                <Grid
                    RowDefinitions="*,3*">
                    <telerik:RadBorder
                        Padding="{ui:EdgeInsets
                            Left={x:Static ui:Dimens.SpacingMd},
                            Right={x:Static ui:Dimens.SpacingXl}}"
                        Style="{x:Static ui:Styles.CustomPaddingRadBorderStyle}">
                        <FlexLayout
                            Padding="{ui:EdgeInsets 
                                Vertical={x:Static ui:Dimens.SpacingSm}}">
                            <Label
                                Style="{x:Static ui:Styles.CenterLabelBold}" />
                            <telerik:RadButton
                                HorizontalOptions="Fill"
                                VerticalOptions="Fill"
                                Padding="{ui:EdgeInsets All={x:Static ui:Dimens.Spacing0}}"
                                Command="{Binding SelectButtonCommand}"
                                CommandParameter="{Binding SelectedGuideItem}"
                                FontSize="{Binding SelectedGuideItem.MedicalCheckName,
                                    Converter={x:Static ui:AppConverters.ResponsiveFontSizeConverter},
                                    ConverterParameter={ui:FontSizeParameters
                                        BaseFontSize={x:Static ui:Dimens.FontSize60},
                                        MaxFontSize={x:Static ui:Dimens.FontSize100},
                                        Ratio=0.2}}"
                                BackgroundColor="{Binding SelectedGuideItem.BackgroundColor}"
                                TextColor="{Binding SelectedGuideItem.TextColor}"
                                Text="{Binding SelectedGuideItem.MedicalCheckName}"
                                Style="{x:Static ui:Styles.GuideSelectButton}" />
                        </FlexLayout>
                    </telerik:RadBorder>

                    <telerik:RadBorder
                        Grid.Row="1"
                        Style="{x:Static ui:Styles.CustomMarginRadBorderStyle}">
                        <!--Guide Items-->
                        <app:GuidanceSelectionListView
                            GuideItems="{Binding GuideItems}"
                            SelectionChangedCommand="{Binding SelectButtonCommand}" />
                    </telerik:RadBorder>
                </Grid>
            </telerik:RadBorder>
        </Grid>

        <!--Footer-->
        <BoxView
            BackgroundColor="{x:Static ui:AppColors.LightCobaltBlue}"
            HeightRequest="{x:Static ui:Dimens.Height100}"
            Grid.Row="2">
        </BoxView>
    </Grid>
</mvvm:BasePage>