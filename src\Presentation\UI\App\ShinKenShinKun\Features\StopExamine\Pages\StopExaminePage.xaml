<?xml version="1.0" encoding="utf-8" ?>
<mvvm:BasePage
    x:Class="ShinKenShinKun.StopExaminePage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:ShinKenShinKun"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:utils="clr-namespace:ShinKenShinKun.Utils;assembly=ShinKenShinKun.Utils"
    Title="StopExaminePage"
    x:DataType="app:StopExaminePageViewModel">
    <Shell.BackButtonBehavior>
        <BackButtonBehavior IsEnabled="False" IsVisible="False" />
    </Shell.BackButtonBehavior>
    <Grid BackgroundColor="{x:Static ui:AppColors.HeaderColor}" RowDefinitions="80, *, Auto">
        <Grid Grid.Row="0" ColumnDefinitions="*, Auto, *">
            <HorizontalStackLayout
                Margin="{x:Static ui:Dimens.SpacingSm2}"
                Padding="{ui:EdgeInsets Left={x:Static ui:Dimens.SpacingXl}}"
                VerticalOptions="Center">
                <app:MaskButtonView ChangeModeCommand="{Binding ChangeModeCommand}" IsMaskMode="{Binding IsMaskMode}" />
            </HorizontalStackLayout>
            <HorizontalStackLayout
                Grid.Column="1"
                HorizontalOptions="Center"
                Spacing="{x:Static ui:Dimens.SpacingLg}">
                <Label Style="{Static ui:Styles.AppHeaderLabelStyle}" Text="{x:Static resources:AppResources.StopExamineScreen}" />
            </HorizontalStackLayout>
            <app:LoginUserView
                Grid.Column="2"
                IsVisible="{Binding IsLogin}"
                LoginId="{Binding LoginId}" />
        </Grid>
        <Grid
            Grid.Row="1"
            BackgroundColor="{x:Static ui:AppColors.ContentBackgroud}"
            ColumnDefinitions="3*,7*"
            ColumnSpacing="{Static ui:Dimens.Spacing10}">
            <app:PatientInfoView
                Grid.Column="0"
                IsItemSelected="{Binding ClientSelected, Converter={x:Static ui:AppConverters.ObjectToBoolConverter}}"
                IsMaskMode="{Binding IsMaskMode}"
                ItemSelected="{Binding ClientSelected}"
                NoteScroll="{Binding NoteScroll}"
                PersonalNoteScroll="{Binding PersonalNoteScroll}"
                ShowNoteEditPopupCommand="{Binding ShowNoteEditPopupCommand}" />

            <Grid Grid.Column="1" Padding="{Static ui:Dimens.Spacing10}">
                <telerik:RadBorder BackgroundColor="{Static ui:AppColors.White}" CornerRadius="{Static ui:Dimens.RadCornerRadius12}">
                    <telerik:RadBorder.Shadow>
                        <Shadow Opacity="0.5" Radius="5" />
                    </telerik:RadBorder.Shadow>
                    <Grid
                        Padding="{Static ui:Dimens.Spacing10}"
                        ColumnDefinitions="7*,3*"
                        ColumnSpacing="{Static ui:Dimens.Spacing10}">
                        <telerik:RadBorder BackgroundColor="{Static ui:AppColors.LightGrayishWhite}" CornerRadius="{Static ui:Dimens.RadCornerRadius12}">
                            <Grid
                                Padding="{ui:EdgeInsets Horizontal={x:Static ui:Dimens.SpacingMd},
                                                        Vertical={x:Static ui:Dimens.SpacingSm}}"
                                RowDefinitions="Auto, *, Auto"
                                RowSpacing="{Static ui:Dimens.Spacing12}">
                                <telerik:RadBorder
                                    x:Name="border"
                                    HorizontalOptions="Start"
                                    Style="{x:Static ui:Styles.BorderPatientTitle}">
                                    <Label
                                        Margin="{ui:EdgeInsets Horizontal={x:Static ui:Dimens.SpacingS}}"
                                        HorizontalTextAlignment="Center"
                                        Style="{x:Static ui:Styles.NotoSansRegularStyle}"
                                        Text="{Static resources:AppResources.InspectionItemSelection}"
                                        VerticalOptions="Center"
                                        VerticalTextAlignment="Center">
                                        <Label.Triggers>
                                            <DataTrigger
                                                Binding="{Binding IsSelectMedicalCheck}"
                                                TargetType="Label"
                                                Value="True">
                                                <Setter Property="Text" Value="{Static resources:AppResources.SelectTheTestType}" />
                                            </DataTrigger>
                                        </Label.Triggers>
                                    </Label>
                                </telerik:RadBorder>
                                <Grid
                                    Grid.Row="1"
                                    RowDefinitions="auto,*"
                                    RowSpacing="{Static ui:Dimens.SpacingXs}">
                                    <Grid
                                        ColumnDefinitions="*,*"
                                        ColumnSpacing="{Static ui:Dimens.SpacingXs}"
                                        HorizontalOptions="Start">
                                        <telerik:RadButton
                                            x:Name="selectAll"
                                            Padding="{ui:EdgeInsets Horizontal={Static ui:Dimens.Spacing40}}"
                                            Command="{Binding SelectAllCommand}"
                                            Style="{Static ui:Styles.StopExamineFunctionRadButtonStyle}"
                                            Text="{Static resources:AppResources.SelectAll}" />
                                        <telerik:RadButton
                                            Grid.Column="1"
                                            Command="{Binding ClearSelectedCommand}"
                                            Style="{Static ui:Styles.StopExamineFunctionRadButtonStyle}"
                                            Text="{Static resources:AppResources.Clear}"
                                            WidthRequest="{Binding Width, Source={Reference selectAll}}" />
                                    </Grid>
                                    <Grid
                                        Grid.Row="1"
                                        RowDefinitions="auto,*"
                                        RowSpacing="{Static ui:Dimens.SpacingXs}">
                                        <telerik:RadBorder
                                            BackgroundColor="{Static ui:AppColors.LightNormalGray}"
                                            HorizontalOptions="Start"
                                            IsVisible="{Binding IsSelectSubMedicalCheck}"
                                            Style="{x:Static ui:Styles.BorderPatientTitle}"
                                            WidthRequest="{Binding Width, Source={Reference border}}">
                                            <Label
                                                Margin="{ui:EdgeInsets Horizontal={x:Static ui:Dimens.SpacingS}}"
                                                HorizontalTextAlignment="Center"
                                                Style="{x:Static ui:Styles.NotoSansRegularStyle}"
                                                Text="{Binding Source={Static utils:AppConstants.MedicalCheckProgressNames}, Path=[0]}"
                                                VerticalOptions="Center"
                                                VerticalTextAlignment="Center" />
                                        </telerik:RadBorder>
                                        <ScrollView
                                            Grid.Row="1"
                                            HorizontalOptions="Fill"
                                            VerticalOptions="Fill">
                                            <FlexLayout
                                                AlignContent="Start"
                                                AlignItems="Start"
                                                BindableLayout.ItemsSource="{Binding MedicalCheckStateDisplay}"
                                                Direction="Row"
                                                JustifyContent="Start"
                                                Wrap="Wrap">
                                                <BindableLayout.ItemTemplate>
                                                    <DataTemplate x:DataType="app:MedicalCheckStateStopModel">
                                                        <telerik:RadButton
                                                            Command="{Binding ChangeSelectedCommand, Source={RelativeSource AncestorType={Type app:StopExaminePageViewModel}}}"
                                                            CommandParameter="{Binding .}"
                                                            HeightRequest="{Binding Source={RelativeSource Self}, Path=FontSize, Converter={Static ui:AppConverters.SizeMultipleConverter}, ConverterParameter=5}"
                                                            IsEnabled="{Binding IsEnable}"
                                                            Style="{Static ui:Styles.MedicalChecklistStopRadButtonStyle}"
                                                            Text="{Binding MedicalCheckState.MedicalCheckName}">
                                                            <telerik:RadButton.Shadow>
                                                                <Shadow
                                                                    Opacity="0.5"
                                                                    Radius="2"
                                                                    Offset="2,2" />
                                                            </telerik:RadButton.Shadow>
                                                            <telerik:RadButton.Triggers>
                                                                <DataTrigger
                                                                    Binding="{Binding IsSelected}"
                                                                    TargetType="telerik:RadButton"
                                                                    Value="True">
                                                                    <Setter Property="BorderColor" Value="{Static ui:AppColors.CyanCornflowerBlue}" />
                                                                    <Setter Property="BackgroundColor" Value="{Static ui:AppColors.AzureishWhiteBlue}" />
                                                                </DataTrigger>
                                                            </telerik:RadButton.Triggers>
                                                            <VisualStateManager.VisualStateGroups>
                                                                <VisualStateGroup x:Name="CommonStates">
                                                                    <VisualState x:Name="Normal" />
                                                                    <VisualState x:Name="Disabled">
                                                                        <VisualState.Setters>
                                                                            <Setter Property="BackgroundColor" Value="{Static ui:AppColors.MediumLightGray}" />
                                                                            <Setter Property="TextColor" Value="{Static ui:AppColors.Black}" />
                                                                            <Setter Property="Shadow" Value="{Static ui:Styles.NoShadow}" />
                                                                        </VisualState.Setters>
                                                                    </VisualState>
                                                                </VisualStateGroup>
                                                            </VisualStateManager.VisualStateGroups>
                                                        </telerik:RadButton>
                                                    </DataTemplate>
                                                </BindableLayout.ItemTemplate>
                                            </FlexLayout>
                                        </ScrollView>
                                    </Grid>
                                </Grid>
                                <Grid Grid.Row="2" ColumnDefinitions="*,*">
                                    <telerik:RadButton
                                        Command="{Binding SelectMedicalCheckCommand}"
                                        HorizontalOptions="Start"
                                        ImageSource="{Static ui:Icons.ArrowCircleLeft24x24}"
                                        IsVisible="{Binding IsSelectSubMedicalCheck}"
                                        Style="{Static ui:Styles.StopExamineFunctionRadButtonStyle}"
                                        Text="{Static resources:AppResources.SelectTheTestType}" />
                                    <telerik:RadButton
                                        Grid.Column="1"
                                        Command="{Binding SelectSubMedicalCheckCommand}"
                                        ContentLayout="Right"
                                        HorizontalOptions="End"
                                        ImageSource="{Static ui:Icons.ArrowCircleRight24x24}"
                                        IsVisible="{Binding IsSelectMedicalCheck}"
                                        Style="{Static ui:Styles.StopExamineFunctionRadButtonStyle}"
                                        Text="{Static resources:AppResources.InspectionItemSelection}" />
                                </Grid>
                            </Grid>
                        </telerik:RadBorder>
                        <telerik:RadBorder
                            Grid.Column="1"
                            BackgroundColor="{Static ui:AppColors.LightGrayishWhite}"
                            CornerRadius="{Static ui:Dimens.Spacing12}">
                            <Grid Padding="{ui:EdgeInsets Horizontal={x:Static ui:Dimens.SpacingMd}, Vertical={x:Static ui:Dimens.SpacingSm}}" RowDefinitions="Auto, *">
                                <telerik:RadBorder
                                    Margin="{ui:EdgeInsets Bottom={x:Static ui:Dimens.SpacingS}}"
                                    HorizontalOptions="Start"
                                    Style="{x:Static ui:Styles.BorderPatientTitle}">
                                    <Label
                                        Margin="{ui:EdgeInsets Horizontal={x:Static ui:Dimens.SpacingS}}"
                                        HorizontalTextAlignment="Center"
                                        Style="{x:Static ui:Styles.NotoSansRegularStyle}"
                                        Text="{Static resources:AppResources.SelectReasonForCancellation}"
                                        VerticalOptions="Center"
                                        VerticalTextAlignment="Center" />
                                </telerik:RadBorder>
                                <VerticalStackLayout Grid.Row="1" BindableLayout.ItemsSource="{Binding Reasons}">
                                    <BindableLayout.ItemTemplate>
                                        <DataTemplate x:DataType="app:MedicalChecklistReasonModel">
                                            <RadioButton
                                                Content="{Binding Reason}"
                                                IsChecked="{Binding IsChecked}"
                                                TextColor="{Static ui:AppColors.Black}" />
                                        </DataTemplate>
                                    </BindableLayout.ItemTemplate>
                                </VerticalStackLayout>
                            </Grid>
                        </telerik:RadBorder>
                    </Grid>
                </telerik:RadBorder>
            </Grid>
        </Grid>
        <Grid
            Grid.Row="2"
            Padding="{x:Static ui:Dimens.SpacingSm2}"
            BackgroundColor="{x:Static ui:AppColors.LightBlue}"
            ColumnDefinitions="Auto,*,Auto"
            HeightRequest="{x:Static ui:Dimens.Spacing100}">
            <app:BackButtonView
                BackCommand="{Binding BackCommand}"
                HorizontalOptions="Start" />
            <HorizontalStackLayout
                Grid.Column="2"
                Spacing="{x:Static ui:Dimens.SpacingMd}">
                <telerik:RadBorder
                    Style="{x:Static ui:Styles.SaveButtonBorderStyle}">
                    <telerik:RadBorder.GestureRecognizers>
                        <TapGestureRecognizer
                            Command="{Binding BackCommand}" />
                    </telerik:RadBorder.GestureRecognizers>
                    <StackLayout
                        HorizontalOptions="Center"
                        Orientation="Vertical"
                        VerticalOptions="Center">
                        <Image
                            HeightRequest="{x:Static ui:Dimens.Height32}"
                            Source="{x:Static ui:Icons.SaveIcon}"
                            WidthRequest="{x:Static ui:Dimens.Width32}" />
                        <Label
                            Style="{x:Static ui:Styles.SaveButtonLabelStyle}" />
                    </StackLayout>
                </telerik:RadBorder>
            </HorizontalStackLayout>
        </Grid>
    </Grid>
</mvvm:BasePage>