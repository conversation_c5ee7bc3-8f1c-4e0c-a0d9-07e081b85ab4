﻿<?xml version="1.0" encoding="utf-8" ?>

<app:BasePage
    x:Class="ShinKenShinKun.LoginPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:resource="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:vm="clr-namespace:ShinKenShinKun"
    x:Name="this"
    Title="LoginPage"
    x:DataType="vm:LoginPageViewModel">
    <Grid
        BackgroundColor="{Static ui:AppColors.Platinum}"
        HorizontalOptions="Fill"
        RowDefinitions="80,*,auto"
        VerticalOptions="Fill">
        <Grid
            BackgroundColor="{Static ui:AppColors.LightCobaltBlue}"
            HorizontalOptions="Fill">
            <Label
                Style="{Static ui:Styles.AppHeaderLabelStyle}"
                Text="{Static resource:AppResources.LoginScreenTitle}" />
            <HorizontalStackLayout
                Margin="{ui:EdgeInsets Right={Static ui:Dimens.Spacing100}}"
                HorizontalOptions="End"
                VerticalOptions="Center">
                <Grid
                    ColumnDefinitions="auto,auto,auto"
                    ColumnSpacing="{Static ui:Dimens.Spacing10}">
                    <Label
                        Style="{Static ui:Styles.OnlineOfflineModeLabelStyle}"
                        Text="{Static resource:AppResources.Online}" />
                    <telerik:RadBorder
                        Grid.Column="1"
                        BackgroundColor="{Static ui:AppColors.HanBlue}"
                        CornerRadius="{Static ui:Dimens.RadBorderCornerRadius4}">
                        <telerik:RadBorder.GestureRecognizers>
                            <TapGestureRecognizer
                                Command="{Binding ChangeModeCommand}" />
                        </telerik:RadBorder.GestureRecognizers>
                        <Grid
                            Padding="{ui:EdgeInsets Vertical={Static ui:Dimens.Spacing1},
                                                    Horizontal={Static ui:Dimens.Spacing3}}"
                            ColumnDefinitions="auto,auto"
                            ColumnSpacing="2">
                            <telerik:RadButton
                                x:Name="onlineModeButton"
                                Command="{Binding ChangeModeCommand}"
                                IsEnabled="{Binding IsOnlineMode}"
                                Style="{Static ui:Styles.OnlineOfflineSwitchModeButton}">
                                <telerik:RadButton.Triggers>
                                    <DataTrigger
                                        Binding="{Binding IsOnlineMode}"
                                        TargetType="telerik:RadButton"
                                        Value="False">
                                        <Setter
                                            Property="BackgroundColor"
                                            Value="{Static ui:AppColors.Transparent}" />
                                    </DataTrigger>
                                </telerik:RadButton.Triggers>
                            </telerik:RadButton>

                            <telerik:RadButton
                                x:Name="offlineModeButton"
                                Grid.Column="1"
                                Command="{Binding ChangeModeCommand}"
                                IsEnabled="{Binding IsOfflineMode}"
                                Style="{Static ui:Styles.OnlineOfflineSwitchModeButton}">
                                <telerik:RadButton.Triggers>
                                    <DataTrigger
                                        Binding="{Binding IsOfflineMode}"
                                        TargetType="telerik:RadButton"
                                        Value="False">
                                        <Setter
                                            Property="BackgroundColor"
                                            Value="{Static ui:AppColors.Transparent}" />
                                    </DataTrigger>
                                </telerik:RadButton.Triggers>
                            </telerik:RadButton>
                        </Grid>
                    </telerik:RadBorder>
                    <Label
                        Grid.Column="2"
                        Style="{Static ui:Styles.OnlineOfflineModeLabelStyle}"
                        Text="{Static resource:AppResources.Offline}" />
                </Grid>
            </HorizontalStackLayout>
        </Grid>
        <Grid
            Grid.Row="1"
            HorizontalOptions="Fill"
            VerticalOptions="Fill">
            <Grid
                HorizontalOptions="Center"
                RowDefinitions="auto,auto,auto"
                RowSpacing="{Static ui:Dimens.Spacing20}"
                VerticalOptions="Center">
                <Label
                    x:Name="loginLabel"
                    Style="{Static ui:Styles.LoginSystemNameLabelStyle}"
                    Text="{Static resource:AppResources.SystemName}" />
                <Button
                    Grid.ColumnSpan="1"
                    MinimumHeightRequest="0"
                    MinimumWidthRequest="0"
                    Opacity="0"
                    ZIndex="-1" />
                <Grid
                    Grid.Row="1"
                    RowDefinitions="auto,auto"
                    RowSpacing="{Static ui:Dimens.Spacing5}"
                    WidthRequest="{Static ui:Dimens.Width350}">
                    <telerik:RadBorder
                        Style="{Static ui:Styles.UserIdPasswordRadBorderStyle}">
                        <Grid
                            ColumnDefinitions="27*,73*"
                            ColumnSpacing="{Static ui:Dimens.Spacing5}">
                            <Label
                                Style="{Static ui:Styles.UserIdPasswordLabelStyle}"
                                Text="{Static resource:AppResources.UserId}" />
                            <telerik:RadEntry
                                Grid.Column="1"
                                BorderThickness="0"
                                HorizontalOptions="Fill"
                                Text="{Binding LoginForm.LoginId}" />
                        </Grid>
                    </telerik:RadBorder>
                    <telerik:RadBorder
                        Grid.Row="1"
                        Style="{Static ui:Styles.UserIdPasswordRadBorderStyle}">
                        <Grid
                            ColumnDefinitions="27*,73*"
                            ColumnSpacing="5">
                            <Label
                                Style="{Static ui:Styles.UserIdPasswordLabelStyle}"
                                Text="{Static resource:AppResources.Password}" />
                            <Grid
                                Grid.Column="1"
                                ColumnDefinitions="*,auto">
                                <telerik:RadEntry
                                    x:Name="passwordEntry"
                                    Grid.ColumnSpan="2"
                                    Padding="{ui:EdgeInsets Right={Static ui:Dimens.Spacing24},
                                                            Left={Static ui:Dimens.SpacingSm}}"
                                    BorderThickness="0"
                                    ClearButtonVisibility="Never"
                                    HorizontalOptions="Fill"
                                    IsPassword="True"
                                    Text="{Binding LoginForm.Password}" />
                                <Image
                                    x:Name="showHidePassWord"
                                    Grid.Column="1"
                                    Margin="{ui:EdgeInsets Right={Static ui:Dimens.SpacingXs}}"
                                    BackgroundColor="{Static ui:AppColors.Transparent}"
                                    HorizontalOptions="End"
                                    Source="{Static ui:Icons.Visibility}"
                                    WidthRequest="{Static ui:Dimens.Spacing20}">
                                    <Image.GestureRecognizers>
                                        <TapGestureRecognizer
                                            Tapped="TapGestureRecognizer_Tapped" />
                                    </Image.GestureRecognizers>
                                </Image>
                            </Grid>
                        </Grid>
                    </telerik:RadBorder>
                </Grid>
                <telerik:RadButton
                    Grid.Row="2"
                    Command="{Binding LoginCommand}"
                    Style="{Static ui:Styles.LoginButtonStyle}"
                    Text="{Static resource:AppResources.Login}"
                    WidthRequest="{Binding Source={Reference loginLabel}, Path=Width}" />
            </Grid>
        </Grid>
        <Grid
            Grid.Row="2">
            <telerik:RadButton
                Command="{Binding OpenQuitAppPopupCommand}"
                Style="{Static ui:Styles.ShutdownButtonStyle}"
                Text="{Static resource:AppResources.Shutdown}" />
        </Grid>
    </Grid>
</app:BasePage>