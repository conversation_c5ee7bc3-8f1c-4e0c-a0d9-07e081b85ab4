﻿using System.Drawing;
namespace ShinKenShinKun.UI;

public partial class Styles
{
    public static Style BorderStyle => CreateStyle<Border>()
        .Set(Border.StrokeThicknessProperty, Dimens.Thickness1)
        .Set(Border.StrokeShapeProperty, new Rectangle())
        .Set(Border.StrokeProperty, AppColors.Transparent);

    public static Style BorderPatient => CreateStyle<RadBorder>()
        .Set(RadBorder.BorderThicknessProperty, Dimens.Thickness1)
        .Set(RadBorder.BorderColorProperty, AppColors.Transparent)
        .Set(VisualElement.BackgroundColorProperty, AppColors.White)
        .Set(VisualElement.HeightRequestProperty, Dimens.Height28)
        .Set(RadBorder.CornerRadiusProperty, Dimens.RadCornerRadius4);

    public static Style BorderPatientTitle => CreateStyle<RadBorder>()
        .Set(RadBorder.BorderColorProperty, AppColors.Transparent)
        .Set(VisualElement.BackgroundColorProperty, AppColors.MediumSlateBlue)
        .Set(VisualElement.HeightRequestProperty, Dimens.Height28)
        .Set(RadBorder.CornerRadiusProperty, Dimens.RadCornerRadius4);

    public static Style HorizontalRule => CreateStyle<BoxView>()
        .Set(VisualElement.HeightRequestProperty, Dimens.Thickness2)
        .Set(BoxView.HorizontalOptionsProperty, LayoutOptions.Fill)
        .Set(VisualElement.BackgroundColorProperty, AppColors.LightBlue);

    public static Style IndividualProgressHorizontalRule => CreateStyle<BoxView>()
        .Set(VisualElement.HeightRequestProperty, Dimens.Thickness1)
        .Set(BoxView.HorizontalOptionsProperty, LayoutOptions.Fill)
        .Set(VisualElement.BackgroundColorProperty, AppColors.LightBlue);
    public static Style IndividualProgressStatusCounterRule => CreateStyle<BoxView>()
        .Set(VisualElement.HeightRequestProperty, Dimens.Thickness2)
        .Set(VisualElement.WidthRequestProperty, Dimens.Width60)
        .Set(BoxView.HorizontalOptionsProperty, LayoutOptions.Fill)
        .Set(VisualElement.BackgroundColorProperty, AppColors.LightBlue);

    public static Style ExaminationMenuBorderStyle => CreateStyle<RadBorder>()
        .Set(RadBorder.BorderThicknessProperty, 0)
        .Set(VisualElement.BackgroundColorProperty, AppColors.LightGray)
        .Set(RadBorder.CornerRadiusProperty, Dimens.CornerRadius16);

    public static Style FunctionButtonsBorderStyle => CreateStyle<RadBorder>()
        .Set(RadBorder.BorderThicknessProperty, 0)
        .Set(RadBorder.PaddingProperty, Dimens.SpacingXxs)
        .Set(VisualElement.BackgroundColorProperty, AppColors.LightGray)
        .Set(RadBorder.CornerRadiusProperty, Dimens.RadCornerRadius10);

    public static Style GuideBorderStyle => CreateStyle<RadBorder>()
        .Set(VisualElement.BackgroundColorProperty, AppColors.Gray600)
        .Set(RadBorder.CornerRadiusProperty, Dimens.CornerRadius16)
        .Set(VisualElement.MinimumWidthRequestProperty, Dimens.Width120)
        .Set(VisualElement.HeightRequestProperty, Dimens.Height58)
        .Set(RadBorder.BorderThicknessProperty, 0);

    public static Style UpdateBorderStyle => CreateStyle<RadBorder>()
        .Set(VisualElement.BackgroundColorProperty, AppColors.Gray600)
        .Set(RadBorder.CornerRadiusProperty, Dimens.CornerRadius16)
        .Set(VisualElement.MinimumWidthRequestProperty, Dimens.Width94)
        .Set(VisualElement.HeightRequestProperty, Dimens.Height58)
        .Set(RadBorder.BorderThicknessProperty, 0);

    public static Style ReserveBorderStyle => CreateStyle<RadBorder>()
        .Set(VisualElement.BackgroundColorProperty, AppColors.Gray600)
        .Set(RadBorder.CornerRadiusProperty, Dimens.CornerRadius16)
        .Set(VisualElement.MinimumWidthRequestProperty, Dimens.Width120)
        .Set(VisualElement.HeightRequestProperty, Dimens.Height58)
        .Set(RadBorder.BorderThicknessProperty, 0);

    public static Style HeaderExamineStatusBorderStyle => CreateStyle<RadBorder>()
        .Set(VisualElement.WidthRequestProperty, Dimens.Width42)
        .Set(VisualElement.HeightRequestProperty, Dimens.Height64)
        .Set(VisualElement.BackgroundColorProperty, AppColors.LightSkyBlue)
        .Set(RadBorder.BorderColorProperty, AppColors.DarkLightBlueGray);

    public static Style ValueExamineStatusBorderStyle => CreateStyle<RadBorder>()
        .Set(VisualElement.WidthRequestProperty, Dimens.Width42)
        .Set(VisualElement.HeightRequestProperty, Dimens.Height42)
        .Set(RadBorder.BorderColorProperty, AppColors.DarkLightBlueGray);

    public static Style FrameBorderStyle => CreateStyle<RadBorder>()
        .Set(VisualElement.BackgroundColorProperty, AppColors.White)
        .Set(VisualElement.ShadowProperty, new Shadow()
        {
            Brush = new SolidColorBrush(AppColors.Black),
            Offset = new Microsoft.Maui.Graphics.Point(0, 0),
            Radius = 3,
            Opacity = 0.3f
        })
        .Set(RadBorder.CornerRadiusProperty, Dimens.CornerRadius12)
        .Set(RadBorder.BorderThicknessProperty, 0);

    public static Style HomeBorderStyle => CreateStyle<RadBorder>()
        .Set(VisualElement.BackgroundColorProperty, AppColors.DarkBlue)
        .Set(RadBorder.CornerRadiusProperty, Dimens.RadCornerRadius10)
        .Set(RadBorder.BorderThicknessProperty, Dimens.Thickness2)
        .Set(RadBorder.BorderColorProperty, AppColors.White)
        .Set(VisualElement.WidthRequestProperty, Dimens.Width80)
        .Set(VisualElement.HeightProperty, Dimens.Height70);

    public static Style AddNoteBorderStyle => CreateStyle<RadBorder>()
        .Set(VisualElement.BackgroundColorProperty, AppColors.DarkSlateBlue)
        .Set(RadBorder.CornerRadiusProperty, Dimens.CornerRadius8)
        .Set(VisualElement.MinimumWidthRequestProperty, Dimens.Width220)
        .Set(VisualElement.HeightRequestProperty, Dimens.Height64)
        .Set(RadBorder.BorderThicknessProperty, 0);

    public static Style CloseEditNotePopupBorderStyle => CreateStyle<RadBorder>()
        .Set(VisualElement.BackgroundColorProperty, AppColors.Black)
        .Set(RadBorder.CornerRadiusProperty, Dimens.CornerRadius8)
        .Set(VisualElement.MinimumWidthRequestProperty, Dimens.Width220)
        .Set(VisualElement.HeightRequestProperty, Dimens.Height64)
        .Set(RadBorder.BorderThicknessProperty, 0);
}