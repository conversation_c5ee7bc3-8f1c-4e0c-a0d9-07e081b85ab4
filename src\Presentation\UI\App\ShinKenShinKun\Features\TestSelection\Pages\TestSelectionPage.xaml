<?xml version="1.0" encoding="utf-8" ?>
<mvvm:BasePage
    x:Class="ShinKenShinKun.TestSelectionPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:ShinKenShinKun"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:resource="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    Title="TestSelectionPage"
    x:DataType="app:TestSelectionPageViewModel">
    <Shell.BackButtonBehavior>
        <BackButtonBehavior
            IsEnabled="False"
            IsVisible="False" />
    </Shell.BackButtonBehavior>
    <Grid
        RowDefinitions="80,*,auto"
        BackgroundColor="{Static ui:AppColors.Platinum}">
        <!--Header-->
        <Grid
            Grid.Row="0"
            BackgroundColor="{Static ui:AppColors.LightCobaltBlue}"
            ColumnDefinitions="*, Auto, *">
            <Grid
                ColumnDefinitions="auto"
                Grid.Column="0"
                Margin="{ui:EdgeInsets Left={x:Static ui:Dimens.SpacingSm2}}"
                VerticalOptions="Center">
                <Entry
                    x:Name="HiddenEntry"
                    Opacity="0"
                    WidthRequest="0"
                    HeightRequest="0" />
                <app:HomeButtonView
                    GoHomeCommand="{Binding GoHomeCommand}" />
            </Grid>
            <Label
                Grid.Column="1"
                Text="{x:Static resource:AppResources.TestSelectionPageHeader}"
                Style="{Static ui:Styles.AppHeaderLabelStyle}" />
        </Grid>

        <!--Content-->
        <ScrollView
            Grid.Row="1"
            Margin="{x:Static ui:Dimens.Spacing20}">
            <FlexLayout
                AlignContent="Center"
                AlignItems="Center"
                BindableLayout.ItemsSource="{Binding MedicalChecklists}"
                Direction="Row"
                JustifyContent="Center"
                Wrap="Wrap">
                <BindableLayout.ItemTemplate>
                    <DataTemplate x:DataType="app:MedicalChecklist">
                        <telerik:RadButton
                            Command="{Binding GotoTestCommand, Source={RelativeSource AncestorType={Type app:TestSelectionPageViewModel}}}"
                            CommandParameter="{Binding .}"
                            HeightRequest="{Binding Source={RelativeSource Self}, Path=FontSize, Converter={Static ui:AppConverters.SizeMultipleConverter}, ConverterParameter=3.5}"
                            Style="{Static ui:Styles.MedicalChecklistRadButtonStyle}"
                            Text="{Binding Name}">
                            <telerik:RadButton.Triggers>
                                <DataTrigger
                                    Binding="{Binding IsSelected}"
                                    TargetType="telerik:RadButton"
                                    Value="False">
                                    <Setter Property="BackgroundColor" Value="{Static ui:AppColors.PaleCerulean}" />
                                    <Setter Property="TextColor" Value="{Static ui:AppColors.HanBlue}" />
                                </DataTrigger>
                                <DataTrigger
                                    Binding="{Binding IsSelected}"
                                    TargetType="telerik:RadButton"
                                    Value="True">
                                    <Setter Property="BackgroundColor" Value="{Static ui:AppColors.DarkBlue}" />
                                    <Setter Property="TextColor" Value="{Static ui:AppColors.White}" />
                                </DataTrigger>
                            </telerik:RadButton.Triggers>
                        </telerik:RadButton>
                    </DataTemplate>
                </BindableLayout.ItemTemplate>
            </FlexLayout>
        </ScrollView>

        <!--Footer-->
        <Grid
            Grid.Row="2"
            Padding="{ui:EdgeInsets Vertical={Static ui:Dimens.Spacing5},
            Horizontal={Static ui:Dimens.Spacing3}}"
            BackgroundColor="{Static ui:AppColors.LightCobaltBlue}">
            <telerik:RadBorder
                BackgroundColor="{x:Static ui:AppColors.GrayBack}"
                CornerRadius="{x:Static ui:Dimens.RadCornerRadius10}"
                Padding="{x:Static ui:Dimens.SpacingSm2}"
                WidthRequest="{x:Static ui:Dimens.Spacing160}"
                HorizontalOptions="Start"
                Grid.Column="0">
                <telerik:RadBorder.GestureRecognizers>
                    <TapGestureRecognizer
                        Command="{Binding BackCommand}" />
                </telerik:RadBorder.GestureRecognizers>
                <StackLayout
                    Orientation="Vertical"
                    HorizontalOptions="Center"
                    VerticalOptions="Center">
                    <Image
                        Source="{x:Static ui:Icons.BackIcon}"
                        HeightRequest="{x:Static ui:Dimens.Height32}"
                        WidthRequest="{x:Static ui:Dimens.Width32}" />
                    <Label
                        Text="{x:Static resource:AppResources.Back}"
                        TextTransform="Uppercase"
                        TextColor="{x:Static ui:AppColors.White}"
                        FontSize="{x:Static ui:Dimens.FontSizeT4}"
                        FontFamily="{x:Static ui:FontNames.NotoSansJPRegular}"
                        HorizontalOptions="Center" />
                </StackLayout>
            </telerik:RadBorder>
        </Grid>
    </Grid>
</mvvm:BasePage>