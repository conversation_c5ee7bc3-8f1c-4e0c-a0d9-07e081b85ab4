﻿namespace ShinKenShinKun;

public partial class TestSelectionPageViewModel(IAppNavigator appNavigator) : NavigationAwareBaseViewModel(appNavigator)
{
    #region ObservableProperty

    [ObservableProperty]
    private ObservableCollection<MedicalChecklist> medicalChecklists = GenerateMedicalCheckList();

    internal Action hiddenEntryFocus;
    #endregion ObservableProperty

    #region RelayCommand
    [RelayCommand]
    public async Task GotoTest(MedicalChecklist medicalCheckName)
    {
        medicalCheckName.IsSelected = true;
        await Task.Delay(100);
        hiddenEntryFocus.Invoke();
        await AppNavigator.NavigateAsync(RouterName.ExamineBeforeAuthPage, false, medicalCheckName.Name);
        medicalCheckName.IsSelected = false;
    }

    [RelayCommand]
    private async Task GoHome()
    {
        await AppNavigator.NavigateAsync(RouterName.HomePageRoot);
    }
    #endregion RelayCommand

    #region Private Method

    private static ObservableCollection<MedicalChecklist> GenerateMedicalCheckList()
    {
        return AppConstants.MedicalCheckNames.Select(x => new MedicalChecklist { Name = x }).ToObservableCollection();
    }

    #endregion Private Method
}