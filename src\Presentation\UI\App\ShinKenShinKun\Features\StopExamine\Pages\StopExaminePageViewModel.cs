﻿namespace ShinKenShinKun;

public partial class StopExaminePageViewModel(
    IAppNavigator appNavigator,
    IAppSettingServices appSettingServices,
    ISessionServices sessionServices
) : NavigationAwareBaseViewModel(appNavigator)
{
    #region ObservableProperty

    [ObservableProperty]
    private ClientInformationModel clientSelected;

    [ObservableProperty]
    private string currentUser = AppConstants.CurrentUser;

    [ObservableProperty]
    private bool isLogin = appSettingServices.AppSettings.IsLogin;

    [ObservableProperty]
    private bool isMaskMode = sessionServices.IsMaskMode;

    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(IsSelectMedicalCheck))]
    private bool isSelectSubMedicalCheck;

    [ObservableProperty]
    private ObservableCollection<MedicalCheckStateStopModel> medicalCheckStateStops;

    [ObservableProperty]
    private ObservableCollection<MedicalCheckStateStopModel> medicalCheckStateDisplay;

    [ObservableProperty]
    private ObservableCollection<MedicalChecklistReasonModel> reasons =
    [
        .. AppConstants.StopReasons.Select(x => new MedicalChecklistReasonModel
        {
            IsChecked = false,
            Reason = x,
        }),
    ];

    [ObservableProperty]
    private ObservableCollection<MedicalCheckStateStopModel> subMedicalCheckStateStops =
    [
        .. AppConstants.SubMedicalCheckNames.Select(x => new MedicalCheckStateStopModel
        {
            MedicalCheckState = new MedicalCheckStateModel { MedicalCheckName = x },
            IsEnable = true,
        }),
    ];

    [ObservableProperty] private ScrollPositionModel noteScroll;
    [ObservableProperty] private ScrollPositionModel personalNoteScroll;

    [ObservableProperty]
    private string testStaff = AppConstants.TestStaff;

    public bool IsSelectMedicalCheck => !IsSelectSubMedicalCheck;

    #endregion ObservableProperty

    #region Override Method

    protected override void OnInit(IDictionary<string, object> query)
    {
        ClientSelected = query.GetData<ClientInformationModel>();
        MedicalCheckStateStops =
        [
            .. ClientSelected.MedicalCheckProgressesDisplays.Select(
                x => new MedicalCheckStateStopModel
                {
                    MedicalCheckState = x,
                    IsEnable = x.TestStatus.MedicalState != MedicalState.Excluded,
                }
            ),
        ];
        MedicalCheckStateDisplay = [.. MedicalCheckStateStops];
        base.OnInit(query);
    }

    public override Task OnAppearingAsync()
    {
        NoteScroll = sessionServices.NoteViewScroll;
        PersonalNoteScroll = sessionServices.PersonalNoteViewScroll;
        return base.OnAppearingAsync();
    }

    public override Task OnDisappearingAsync()
    {
        sessionServices.NoteViewScroll = NoteScroll;
        sessionServices.PersonalNoteViewScroll = PersonalNoteScroll;
        return base.OnDisappearingAsync();
    }

    #endregion Override Method

    #region RelayCommand

    [RelayCommand]
    private void ChangeMode()
    {
        IsMaskMode = !IsMaskMode;
        sessionServices.IsMaskMode = IsMaskMode;
    }

    [RelayCommand]
    private void ChangeSelected(MedicalCheckStateStopModel medicalCheck)
    {
        medicalCheck.IsSelected = !medicalCheck.IsSelected;
    }

    [RelayCommand]
    private void ClearSelected()
    {
        if(IsSelectMedicalCheck)
        {
            foreach(var item in MedicalCheckStateStops.Where(item => item.IsEnable))
            {
                item.IsSelected = false;
            }
        }
        else
        {
            foreach(var item in SubMedicalCheckStateStops)
            {
                item.IsSelected = false;
            }
        }
    }

    [RelayCommand]
    private void SelectAll()
    {
        if(IsSelectMedicalCheck)
        {
            foreach(var item in MedicalCheckStateStops.Where(item => item.IsEnable))
            {
                item.IsSelected = true;
            }
        }
        else
        {
            foreach(var item in SubMedicalCheckStateStops)
            {
                item.IsSelected = true;
            }
        }
    }

    [RelayCommand]
    private void SelectSubMedicalCheck()
    {
        IsSelectSubMedicalCheck = !IsSelectSubMedicalCheck;
        foreach(var item in SubMedicalCheckStateStops)
        {
            item.IsSelected = true;
        }
        MedicalCheckStateDisplay = [.. SubMedicalCheckStateStops];
    }

    [RelayCommand]
    private void SelectMedicalCheck()
    {
        IsSelectSubMedicalCheck = !IsSelectSubMedicalCheck;
        MedicalCheckStateDisplay = [.. MedicalCheckStateStops];
    }

    [RelayCommand]
    private async Task ShowNoteEditPopup()
    {
        if(ClientSelected != null)
        {
            var popup = new NoteEditPopup();
            popup.Note = ClientSelected.Note;
            await AppNavigator.ShowPopupAsync(popup);
        }
    }

    #endregion RelayCommand
}