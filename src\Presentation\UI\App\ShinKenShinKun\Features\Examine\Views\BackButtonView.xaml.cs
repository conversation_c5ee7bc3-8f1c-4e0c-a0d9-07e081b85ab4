namespace ShinKenShinKun;

public partial class BackButtonView : RadBorder
{
    public static readonly BindableProperty BackCommandProperty = BindableProperty.Create(
        nameof(BackCommand),
        typeof(IRelayCommand),
        typeof(HomeButtonView),
        defaultBindingMode: BindingMode.TwoWay);
    public BackButtonView()
    {
        InitializeComponent();
    }
    public IRelayCommand BackCommand
    {
        get => (IRelayCommand)GetValue(BackCommandProperty);
        set => SetValue(BackCommandProperty, value);
    }
}