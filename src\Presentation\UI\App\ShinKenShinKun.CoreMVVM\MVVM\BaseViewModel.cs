﻿namespace ShinKenShinKun.CoreMVVM;

public abstract partial class BaseViewModel : ObservableRecipient
{
    [ObservableProperty]
    protected bool isBusy;

    protected IAppNavigator AppNavigator { get; }

    protected BaseViewModel(IAppNavigator appNavigator)
    {
        AppNavigator = appNavigator;
    }

    public virtual Task OnAppearingAsync()
    {
        Log.Information($"{GetType().Name}.{nameof(OnAppearingAsync)}");

        return Task.CompletedTask;
    }

    public virtual Task OnDisappearingAsync()
    {
        Log.Information($"{GetType().Name}.{nameof(OnDisappearingAsync)}");

        return Task.CompletedTask;
    }

    [RelayCommand]
    protected virtual Task BackAsync() => AppNavigator.GoBackAsync(data: GetType().FullName);
}