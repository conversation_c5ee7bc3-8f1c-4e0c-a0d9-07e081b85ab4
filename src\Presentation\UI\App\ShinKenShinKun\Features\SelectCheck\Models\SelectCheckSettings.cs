namespace ShinKenShinKun;

public class SelectCheckSettings
{
    public List<SelectItemModel> SelectItems { get; set; } = [];
    public List<ConfigItemModel> ConfigItems { get; set; } = [];

    public List<SelectCheckModel> GetCheckItems()
    {
        return SelectItems?.Select(item => new SelectCheckModel
        {
            Id = item.TermId,
            Name = item.Name
        }).ToList() ?? new List<SelectCheckModel>();
    }
}

public class SelectItemModel
{
    public string TermId { get; set; }
    public string Name { get; set; }
}

public class SelectCheckModel
{
    public string Id { get; set; }
    public string Name { get; set; }
    public bool IsSelected { get; set; }
}

public class ConfigItemModel
{
    public string CheckId { get; set; }
    public string Name { get; set; }
    public List<ShinKenShinKun.DataAccess.AreaModel> AreaSettings { get; set; } = new List<ShinKenShinKun.DataAccess.AreaModel>();
}
