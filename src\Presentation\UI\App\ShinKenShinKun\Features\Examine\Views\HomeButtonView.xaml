<?xml version="1.0" encoding="utf-8" ?>
<telerik:RadBorder
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.HomeButtonView"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    Style="{x:Static ui:Styles.HomeBorderStyle}"
    x:Name="this">
    <VerticalStackLayout
        HorizontalOptions="Center"
        VerticalOptions="Center">
        <Image
            Source="{x:Static ui:Icons.Home40X40}"
            Aspect="AspectFit"
            HeightRequest="{x:Static ui:Dimens.Height32}" />
        <Label
            Style="{x:Static ui:Styles.LabelHomeStyle}"
            HorizontalOptions="Center" />
    </VerticalStackLayout>
    <telerik:RadBorder.GestureRecognizers>
        <TapGestureRecognizer
            Command="{Binding GoHomeCommand, 
                    Source={x:Reference this}}" />
    </telerik:RadBorder.GestureRecognizers>
</telerik:RadBorder>
