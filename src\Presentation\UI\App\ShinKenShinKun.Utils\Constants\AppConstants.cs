﻿namespace ShinKenShinKun.Utils;

public sealed class AppConstants
{
    internal const string EncryptKey = "3Z2IOPV89EB96OWO5IEWMAX9IRFWI0IR";
    internal const string EncryptIV = "014BSQ4ELNRGDCBF";
    public const string ClientInformationExcelFile = "ClientInformation.xlsx";
    public const string GuidanceExcelFile = "GuidanceItem.xlsx";
    public const string NumberPadExcelFile = "NumberPad.xlsx";
    public const string StandardMasterKeySheetName = "StandardMasterKey";
    public const string CustomMasterKeySheetName = "CustomMasterKey";
    public const string SettingNumberPadSheetName = "SettingNumberPad";
    public const string StandardMasterKeySettingSheetName = "StandardMasterKeySetting";
    public const string CustomMasterKeySettingSheetName = "CustomMasterKeySetting";
    public const string Add = "+";
    public const string Subtract = "-";
    public const string Decimal = ".";
    public const char CharAdd = '+';
    public const char CharSubtract = '-';
    public const char CharDecimal = '.';
    public const string Enter = "Enter";
    public const string Backspace = "Backspace";
    public const string Delete = "Delete";

    public static readonly List<string> MedicalCheckNames =
    [
        "計測",
        "腹囲",
        "血圧",
        "視力",
        "眼圧",
        "心電図",
        "肺機能",
        "採血",
        "眼底",
        "超音波",
        "CT",
        "MRI",
        "胸部X線",
        "胃部X線",
        "内視鏡",
        "握力",
        "心電図",
        "骨密度",
        "遠点視力",
        "近点視力",
    ];

    public static readonly List<string> MedicalCheckProgressNames =
    [
        "計測",
        "腹囲",
        "血圧",
        "視力",
        "眼圧",
        "心電図",
        "肺機能",
        "採血",
        "眼底",
        "超音波",
        "CT",
        "MRI",
        "胸部X線",
        "胃部X線",
        "内視鏡",
        "握力",
        "尿",
        "骨密度",
        "マンモ",
        "胸部CT",
        "頭部CT",
        "腹部CT",
        "大腸CT",
        "腹部エコー",
        "頸動脈エコー",
        "乳房エコー",
        "甲状腺エコー",
        "心臓エコー",
        "診察",
        "眼位",
        "オートレフ",
        "調節近点",
    ];

    public static readonly List<string> SubMedicalCheckNames = ["身長", "体重", "BMI"];

    public static readonly List<string> StopReasons =
    [
        "全キャンセル",
        "本人希望",
        "急用",
        "体調不良",
        "検査困難",
        "直近受診あり",
        "機械故障",
        "その他",
    ];

    public static readonly List<string> NoteMasters =
    [
        "アルコール禁",
        "臥床採血希望",
        "補聴器着用",
        "筆談対応",
        "日本語不可",
        "義眼",
        "クレーマー",
    ];

    public static readonly List<string> MedicalCheckFilters = ["計測", "視力", "血圧"];

    public const string LoginScreenCode = "CE-001";
    public const string HomeScreenCode = "CO-001";
    public const string TestSelectionScreenCode = "MC-001";
    public const string ExamineBeforeAuthScreenCode = "MC-002";
    public const string IndividualProgressScreenCode = "PR-001";
    public const string PatientPendingScreenCode = "SU-001";
    public const string StopExamineScreenCode = "SU-004";
    public const string GuidanceAdjustmentScreenCode = "GU-001";
    public const string OverallProgressScreenCode = "Monitoring";
    public const string Device = "計測";
    public const string TestStaff = "ログインユーザ";
    public const string CurrentUser = "テスト スタッフ";

    //TODO: Rename base on Screen name
    public static readonly List<string> DefaultScreen =
    [
        TestSelectionScreenCode,
        ExamineBeforeAuthScreenCode,
        IndividualProgressScreenCode,
        PatientPendingScreenCode,
        GuidanceAdjustmentScreenCode,
        OverallProgressScreenCode,
    ];

    public static readonly List<string> TestScreen = [ExamineBeforeAuthScreenCode];

    #region Exception

    public const string AppSource = "ShinKenShinKun";
    public const string Application = "Application";
    public const string ApplicationError = "Application Error";
    public const string EventViewerEntry = "Event Viewer Entry: ";
    public const string FailedEventViewer = "Failed to read Event Viewer logs";
    public const string FirstChanceException = "First Chance Exception";
    public const string ResourceEnd = "Resource ending";
    public const string URI = "URI";
    public const string ObjectDisposedException = "ObjectDisposedException detected: An object was used after it was disposed.";
    public const string ThreadConflict = "Thread conflict detected";
    public const string ApplicationDomainUnload = "Application domain is unloading";
    public const string ApplicationExit = "Application is exiting";
    public const string AccessViolation = "Access Violation exception in Microsoft.UI.Xaml caused the app to crash";
    public const string UnhandledException = "Unhandled exception";
    public const string UnobservedTask = "Unobserved task exception";

    #endregion Exception
}