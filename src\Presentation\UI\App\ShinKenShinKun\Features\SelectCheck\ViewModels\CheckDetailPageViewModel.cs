using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using Microsoft.Maui.Controls;
using ShinKenShinKun.CoreMVVM;

namespace ShinKenShinKun;

public class CheckDetailPageViewModel : BaseViewModel, IQueryAttributable
{
    private readonly IAppSettingServices _appSettingServices;
    private string _checkId;
    private string _checkName;
    private ObservableCollection<ShinKenShinKun.DataAccess.AreaModel> _areaSettings;

    public CheckDetailPageViewModel(IAppNavigator appNavigator, IAppSettingServices appSettingServices) : base(appNavigator)
    {
        _appSettingServices = appSettingServices;
        _areaSettings = new ObservableCollection<ShinKenShinKun.DataAccess.AreaModel>();

        // Initialize commands
        GoBackCommand = new Command(OnGoBack);
        GoHomeCommand = new Command(OnGoHome);
        CancelCommand = new Command(OnCancel);
        SaveCommand = new Command(OnSave);
    }



    public string CheckId
    {
        get => _checkId;
        set
        {
            if (_checkId != value)
            {
                _checkId = value;
                OnPropertyChanged();
                LoadCheckData();
            }
        }
    }

    public string CheckName
    {
        get => _checkName;
        set
        {
            if (_checkName != value)
            {
                _checkName = value;
                OnPropertyChanged();
            }
        }
    }

    public ObservableCollection<ShinKenShinKun.DataAccess.AreaModel> AreaSettings
    {
        get => _areaSettings;
        set
        {
            if (_areaSettings != value)
            {
                _areaSettings = value;
                OnPropertyChanged();
            }
        }
    }

    public ICommand GoBackCommand { get; }
    public ICommand GoHomeCommand { get; }
    public ICommand CancelCommand { get; }
    public ICommand SaveCommand { get; }

    public void Initialize(string checkId, string checkName)
    {
        CheckId = checkId;
        CheckName = checkName;
    }

    public void ApplyQueryAttributes(IDictionary<string, object> query)
    {
        if (query.TryGetValue("checkId", out var checkId))
        {
            CheckId = checkId?.ToString();
        }

        if (query.TryGetValue("checkName", out var checkName))
        {
            CheckName = checkName?.ToString();
        }
    }

    private void LoadCheckData()
    {
        if (string.IsNullOrEmpty(_checkId)) return;

        try
        {
            var selectCheckSettings = _appSettingServices.GetSelectCheckSettings();
            var configItem = selectCheckSettings.ConfigItems?.FirstOrDefault(c => c.CheckId == _checkId);
            
            if (configItem?.AreaSettings != null)
            {
                AreaSettings.Clear();
                foreach (var area in configItem.AreaSettings.OrderBy(a => a.Order))
                {
                    AreaSettings.Add(area);
                }
            }
        }
        catch (Exception ex)
        {
            // Handle error - could show error message to user
            System.Diagnostics.Debug.WriteLine($"Error loading check data: {ex.Message}");
        }
    }

    private async Task GoBackAsync()
    {
        try
        {
            await Shell.Current.GoToAsync("..");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Navigation error: {ex.Message}");
        }
    }

    private async void OnGoBack()
    {
        await GoBackAsync();
    }

    private async void OnGoHome()
    {
        try
        {
            // Navigate to home/login page
            await Shell.Current.GoToAsync("//LoginPage");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Navigation error: {ex.Message}");
        }
    }

    private async void OnCancel()
    {
        // Could show confirmation dialog here
        await GoBackAsync();
    }

    private async void OnSave()
    {
        try
        {
            // Here you would implement the save logic
            // For now, just navigate back
            await Shell.Current.DisplayAlert("保存", "データが保存されました", "OK");
            await GoBackAsync();
        }
        catch (Exception ex)
        {
            await Shell.Current.DisplayAlert("エラー", $"保存に失敗しました: {ex.Message}", "OK");
        }
    }


}


